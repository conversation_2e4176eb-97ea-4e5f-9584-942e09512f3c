#!/usr/bin/env python3
"""
Test script to demonstrate the get_user_transaction_summary function.

This script shows various examples of how to use the function to analyze
user transaction data from the CSV file.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

from utils import get_user_transaction_summary


def print_user_summary(user_id, title=None):
    """Helper function to print user transaction summary in a formatted way."""
    if title:
        print(f"\n{title}")
        print("=" * len(title))

    result = get_user_transaction_summary(user_id)

    if "error" in result:
        print(f"❌ Error for user {user_id}: {result['error']}")
        return result

    if result["total_transactions"] == 0:
        print(f"📭 No transactions found for user_id: {user_id}")
        return result

    print(f"👤 User ID: {result['user_id']}")
    print(f"📊 Total Transactions: {result['total_transactions']:,}")
    print(f"💰 Total Value: ₦{result['total_value_naira']:,.2f}")

    # Calculate success rate
    successful_count = result["by_status"].get("Successful", {}).get("count", 0)
    success_rate = (
        (successful_count / result["total_transactions"]) * 100
        if result["total_transactions"] > 0
        else 0
    )
    print(f"✅ Success Rate: {success_rate:.1f}% ({successful_count:,} successful)")

    print(f"\n📈 Breakdown by Status:")
    status_order = ["Successful", "Failed", "InvalidMsisdn", "Pending"]
    for status in status_order:
        if status in result["by_status"]:
            data = result["by_status"][status]
            percentage = (data["count"] / result["total_transactions"]) * 100
            print(
                f"  • {status}: {data['count']:,} transactions ({percentage:.1f}%), ₦{data['value_naira']:,.2f}"
            )

    print(f"\n🏷️  Breakdown by Type:")
    for tx_type, data in result["by_type"].items():
        percentage = (data["count"] / result["total_transactions"]) * 100
        print(
            f"  • {tx_type}: {data['count']:,} transactions ({percentage:.1f}%), ₦{data['value_naira']:,.2f}"
        )

    return result


def main():
    """Test the get_user_transaction_summary function with various scenarios."""

    print("🧪 Testing get_user_transaction_summary function")
    print("=" * 60)

    # Test with the most active user (user_id = 5)
    print_user_summary("5", "📊 Most Active User (ID: 5)")

    # Test with a moderately active user (user_id = 20)
    print_user_summary("20", "📊 Moderately Active User (ID: 20)")

    # Test with a less active user (user_id = 13)
    print_user_summary("13", "📊 Less Active User (ID: 13)")

    # Test with a non-existent user
    print_user_summary("99999", "📊 Non-existent User (ID: 99999)")

    # Demonstrate comparison between users
    print(f"\n🔍 User Comparison Summary")
    print("=" * 30)

    users_to_compare = ["5", "20", "13"]
    comparison_data = []

    for user_id in users_to_compare:
        result = get_user_transaction_summary(user_id)
        if "error" not in result and result["total_transactions"] > 0:
            successful_count = result["by_status"].get("Successful", {}).get("count", 0)
            success_rate = (successful_count / result["total_transactions"]) * 100
            comparison_data.append(
                {
                    "user_id": user_id,
                    "total_transactions": result["total_transactions"],
                    "total_value": result["total_value_naira"],
                    "success_rate": success_rate,
                }
            )

    # Sort by total value descending
    comparison_data.sort(key=lambda x: x["total_value"], reverse=True)

    print(
        f"{'User ID':<8} {'Transactions':<12} {'Total Value (₦)':<15} {'Success Rate':<12}"
    )
    print("-" * 50)
    for data in comparison_data:
        print(
            f"{data['user_id']:<8} {data['total_transactions']:<12,} {data['total_value']:<15,.0f} {data['success_rate']:<12.1f}%"
        )

    print(f"\n💡 Usage Examples:")
    print("=" * 20)
    print("# Basic usage:")
    print("from app.utils import get_user_transaction_summary")
    print("result = get_user_transaction_summary('5')")
    print("print(f\"User has {result['total_transactions']} transactions\")")
    print()
    print("# Check for successful transactions:")
    print("if 'Successful' in result['by_status']:")
    print("    successful = result['by_status']['Successful']")
    print(
        "    print(f\"Successful: {successful['count']} worth ₦{successful['value_naira']:,.2f}\")"
    )


if __name__ == "__main__":
    main()
