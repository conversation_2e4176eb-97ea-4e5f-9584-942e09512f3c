from datetime import datetime
import uuid

from fastapi import HTTPException, status
from sqlmodel import (
    Field,
    Session,
    SQLModel,
    select,
)


# Base model for reuse (to be inherited ny other models).
class DefaultModel(SQLModel):
    uid: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    deleted: bool = Field(default=False)


# Helper function to get a model instance by id.
def get_instance_by_id(session: Session, model: type, id: uuid.uuid4):
    if not isinstance(model, type):
        raise TypeError(f"Expected a class for model, got {type(model)}")
    instance = session.exec(select(model).where(model.id == id)).first()
    if not instance:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=f"{model.__name__} not found."
        )
    return instance


# Helper function to get a model instance by user_id.
def get_instance_by_user_id(session: Session, model: type, user_id: uuid.uuid4):
    if not isinstance(model, type):
        raise TypeError(f"Expected a class for model, got {type(model)}")
    instance = session.exec(select(model).where(model.user_id == user_id)).first()
    if not instance:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=f"{model.__name__} not found."
        )
    return instance
