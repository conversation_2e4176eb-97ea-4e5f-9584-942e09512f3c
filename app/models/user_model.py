from typing import Optional

from pydantic import (
    BaseModel,
    EmailStr,
    constr,
    field_validator,
)
from sqlmodel import Field

from app.models import *


# Create your model(s) here.
class User(DefaultModel, table=True):
    first_name: str = Field(max_length=255)
    last_name: str = Field(max_length=255)
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_verified: bool = Field(default=False)
    hashed_password: str
    is_admin: bool = Field(default=False)
    is_superuser: bool = Field(default=False)

    def __str__(self) -> str:
        return self.email


class EmailValidator(BaseModel):
    email: EmailStr


class UserResponse(EmailValidator):
    first_name: str
    last_name: str

    @field_validator("first_name", "last_name")
    def title_case_format(cls, value: str) -> str:
        if value:
            return value.title()
        return value


class PasswordValidator(BaseModel):
    password: str = constr(max_length=10, min_length=8)
    confirm_password: str = constr(max_length=10, min_length=8)


class UserValidator(UserResponse, PasswordValidator):
    pass


class RefreshTokenValidator(BaseModel):
    refresh_token: str
