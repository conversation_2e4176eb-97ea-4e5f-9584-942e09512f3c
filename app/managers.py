import os
import stat
from typing import Optional

from decouple import config
import paramiko

from app.config import (
    API_AUTH,
    BASE_DIR,
    ENVIRONMENT,
)
from app.utils import make_request


# Create your manager(s) here.
class HisaWalletManager:
    """
    Manager for fetching data from Hisa wallets.
    """

    def __init__(self, **kwargs):
        self.hisa1_base_url = "https://api.myhisa.com/api/third-party/services/reports"
        self.hisa2_base_url = (
            "http://94.229.72.28:3000/api/third-party/services/reports"
        )
        self.headers = {"Accept": "application/json"}

    def fetch_users(
        self,
        hisa_one: Optional[bool] = False,
        hisa_two: Optional[bool] = False,
    ):
        """NOTE: Sample Response below.
        {
            "status_code":200,
            "status":true,
            "data":{
                "status":true,
                "status_code":0,
                "message":"List of users",
                "data":[
                    {
                        "id":1,
                        "name":"<PERSON>",
                        "email":"<EMAIL>",
                        "wallet":{
                            "id":1,
                            "user_id":1,
                            "amount":980
                        }
                    },
                    ...
                ]
            },
            "error":"None"
        }
        """
        if not hisa_one and not hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "No Hisa service was specified.",
            }
        if hisa_one and hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "Specify only one Hisa service (hisa_one or hisa_two).",
            }

        headers = {"Accept": "application/json"}
        if hisa_one:
            url = f"{self.hisa1_base_url}/users"
            headers["api-auth"] = API_AUTH
        elif hisa_two:
            url = f"{self.hisa2_base_url}/users"
            headers["Authorization"] = f"Bearer {API_AUTH}"
        params = {"url": url, "headers": headers, "data": {}}
        response = make_request("GET", params)
        return response

    def fetch_wallets(
        self,
        hisa_one: Optional[bool] = False,
        hisa_two: Optional[bool] = False,
    ):
        """NOTE: Sample Response below.
        {
            "status_code":200,
            "status":true,
            "data":{
                "status":true,
                "status_code":0,
                "message":"List of wallets",
                "data":[
                    {
                        "id":1,
                        "user_id":1,
                        "amount":980,
                        "user":{
                            "id":1,
                            "name":"Abel Onuoha",
                            "email":"<EMAIL>"
                        }
                    },
                    ...
                ]
            },
            "error":"None"
        }
        """
        if not hisa_one and not hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "No Hisa service was specified.",
            }
        if hisa_one and hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "Specify only one Hisa service (hisa_one or hisa_two).",
            }

        headers = {"Accept": "application/json"}
        if hisa_one:
            url = f"{self.hisa1_base_url}/wallets"
            headers["api-auth"] = API_AUTH
        elif hisa_two:
            url = f"{self.hisa2_base_url}/wallets"
            headers["Authorization"] = f"Bearer {API_AUTH}"
        params = {"url": url, "headers": headers, "data": {}}
        response = make_request("GET", params)
        return response

    def daily_balances(
        self,
        user_id: str,
        from_date: str,
        to_date: str,
        hisa_one: Optional[bool] = False,
        hisa_two: Optional[bool] = False,
    ):
        """NOTE: Sample Response below.
        {
            "status_code":200,
            "status":true,
            "data":{
                "status":true,
                "status_code":0,
                "message":"Statement",
                "data":[
                    {
                        "date":"2025-07-29",
                        "opening_balance":90980,
                        "closing_balance":90980,
                        "debit_count":0,
                        "debit_amount":0,
                        "credit_count":0,
                        "credit_amount":0
                    },
                    ...
                ]
            },
            "error":"None"
            }
        """
        if not hisa_one and not hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "No Hisa service was specified.",
            }
        if hisa_one and hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "Specify only one Hisa service (hisa_one or hisa_two).",
            }
        headers = {"Accept": "application/json"}
        if hisa_one:
            url = f"{self.hisa1_base_url}/statement/daily?user_id={user_id}&from={from_date}&to={to_date}"
            headers["api-auth"] = API_AUTH
        elif hisa_two:
            url = f"{self.hisa2_base_url}/statement/daily?user_id={user_id}&from_date={from_date}&to_date={to_date}"
            headers["Authorization"] = f"Bearer {API_AUTH}"
        params = {"url": url, "headers": headers, "data": {}}
        response = make_request("GET", params)
        return response

    def monthly_balances(
        self,
        user_id: str,
        from_date: str,
        to_date: str,
        hisa_one: Optional[bool] = False,
        hisa_two: Optional[bool] = False,
    ):
        """NOTE: Sample Response below.
        {
            "status_code":200,
            "status":true,
            "data":{
                "status":true,
                "status_code":0,
                "message":"Statement",
                "data":[
                    {
                        "month":"May 2025",
                        "date":"2025-05",
                        "opening_balance":26550,
                        "closing_balance":822000980,
                        "debit_count":3,
                        "debit_amount":25570,
                        "credit_count":1,
                        "credit_amount":"822000000"
                    },
                    ...
                ]
            },
            "error":"None"
            }
        """
        if not hisa_one and not hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "No Hisa service was specified.",
            }
        if hisa_one and hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "Specify only one Hisa service (hisa_one or hisa_two).",
            }
        headers = {"Accept": "application/json"}
        if hisa_one:
            url = f"{self.hisa1_base_url}/statement/monthly?user_id={user_id}&from={from_date}&to={to_date}"
            headers["api-auth"] = API_AUTH
        elif hisa_two:
            url = f"{self.hisa2_base_url}/statement/monthly?user_id={user_id}&from_date={from_date}&to_date={to_date}"
            headers["Authorization"] = f"Bearer {API_AUTH}"
        params = {"url": url, "headers": headers, "data": {}}
        response = make_request("GET", params)
        return response

    def wallet_transactions(
        self,
        user_id: str,
        from_date: str,
        to_date: str,
        hisa_one: Optional[bool] = False,
        hisa_two: Optional[bool] = False,
    ):
        """NOTE: Sample Response below.
        {
            "status_code":200,
            "status":true,
            "data":{
                "status":true,
                "status_code":0,
                "message":"Daily wallet summary",
                "data":[
                    {
                        "date":"2025-08-09",
                        "type":"debit",
                        "total_amount":"3156090"
                    },
                    {
                        "date":"2025-08-08",
                        "type":"credit",
                        "total_amount":"10000000"
                    },
                    ...
                ]
            },
            "error":"None"
            }
        """
        if not hisa_one and not hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "No Hisa service was specified.",
            }
        if hisa_one and hisa_two:
            return {
                "status_code": 400,
                "status": False,
                "data": None,
                "error": "Specify only one Hisa service (hisa_one or hisa_two).",
            }
        headers = {"Accept": "application/json"}
        if hisa_one:
            url = f"{self.hisa1_base_url}/wallet-history?user_id={user_id}&from={from_date}&to={to_date}"
            headers["api-auth"] = API_AUTH
        elif hisa_two:
            url = f"{self.hisa2_base_url}/wallet-history?user_id={user_id}&from={from_date}&to={to_date}"
            headers["Authorization"] = f"Bearer {API_AUTH}"
        params = {"url": url, "headers": headers, "data": {}}
        response = make_request("GET", params)
        return response


class HisaSFTPManager:
    """
    Manager for fetching data from Hisa Logs.
    """

    def __init__(self):
        self.hisa1_host = config("SFTP_HISA1_HOST")
        self.hisa2_host = config("SFTP_HISA2_HOST")
        self.hisasftp_username = config("SFTP_USER")
        self.hisasftp_password = config("SFTP_PASSWORD")

    def connect(
        self, hisa_one: Optional[bool] = False, hisa_two: Optional[bool] = False
    ):
        if not hisa_one and not hisa_two:
            raise ValueError("No Hisa service was specified.")
        if hisa_one and hisa_two:
            raise ValueError("Specify only one Hisa service (hisa_one or hisa_two).")

        host = self.hisa1_host if hisa_one else self.hisa2_host
        try:
            transport = paramiko.Transport(host)
            transport.connect(
                username=self.hisasftp_username, password=self.hisasftp_password
            )
            return paramiko.SFTPClient.from_transport(transport)
        except Exception as e:
            raise ConnectionError(f"Failed to connect to Hisa SFTP Server: {e}")

    def check_logs_exist_locally(self, desired_date: str):
        """
        Check if transaction logs for the specified date exist locally.

        Args:
            desired_date (str): Date in YYYY-MM-DD format

        Returns:
            dict: Contains status and details about local log availability
        """
        action_dir = str(desired_date)
        local_path = f"{BASE_DIR}/reports/hisa_logs/{action_dir}"
        transactions_file = f"{local_path}/{desired_date}_transactions.csv"

        if not os.path.exists(local_path):
            return {
                "exists": False,
                "message": f"Log directory for {desired_date} does not exist locally",
                "transactions_file_exists": False,
                "local_path": local_path,
                "transactions_file": transactions_file,
            }

        transactions_exists = os.path.exists(transactions_file)

        if transactions_exists:
            # Check if file is not empty and has valid content
            try:
                file_size = os.path.getsize(transactions_file)
                if file_size == 0:
                    return {
                        "exists": False,
                        "message": f"Transaction file exists but is empty: {transactions_file}",
                        "transactions_file_exists": False,
                        "local_path": local_path,
                        "transactions_file": transactions_file,
                    }

                # Quick check if it's a valid CSV by reading first line
                with open(transactions_file, "r") as f:
                    first_line = f.readline().strip()
                    if not first_line or "user_id" not in first_line.lower():
                        return {
                            "exists": False,
                            "message": f"Transaction file exists but appears invalid: {transactions_file}",
                            "transactions_file_exists": False,
                            "local_path": local_path,
                            "transactions_file": transactions_file,
                        }

                return {
                    "exists": True,
                    "message": f"Transaction logs for {desired_date} found locally",
                    "transactions_file_exists": True,
                    "local_path": local_path,
                    "transactions_file": transactions_file,
                    "file_size": file_size,
                }

            except Exception as e:
                return {
                    "exists": False,
                    "message": f"Error checking transaction file: {str(e)}",
                    "transactions_file_exists": False,
                    "local_path": local_path,
                    "transactions_file": transactions_file,
                }

        return {
            "exists": False,
            "message": f"Transaction file not found: {transactions_file}",
            "transactions_file_exists": False,
            "local_path": local_path,
            "transactions_file": transactions_file,
        }

    def download_logs(self, sftp, desired_date: str):
        action_dir = str(desired_date)
        remote_path = f"/var/log/hisa_reports/{action_dir}"

        local_path = f"{BASE_DIR}/reports/hisa_logs/{action_dir}"

        # Ensure base local directory exists
        os.makedirs(local_path, exist_ok=True)

        try:
            self._download_directory(sftp, remote_path, local_path)
            return "Logs downloaded successfully."
        except Exception as e:
            return f"Failed to download logs: {e}"

    def _download_directory(self, sftp, remote_dir, local_dir):
        """
        Recursively download the contents of a remote directory to a local directory.
        """
        os.makedirs(local_dir, exist_ok=True)

        for item in sftp.listdir_attr(remote_dir):
            remote_item_path = os.path.join(remote_dir, item.filename).replace(
                "\\", "/"
            )
            local_item_path = os.path.join(local_dir, item.filename)

            if stat.S_ISDIR(item.st_mode):  # Directory
                self._download_directory(sftp, remote_item_path, local_item_path)
            else:  # File
                sftp.get(remote_item_path, local_item_path)
                print(f"Downloaded: {remote_item_path} -> {local_item_path}")
