import io
import os
from typing import Optional

from fastapi import (
    Depends,
    FastAPI,
    File,
    Form,
    HTTPException,
    UploadFile,
    status,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.staticfiles import StaticFiles
import pandas as pd
from sqlmodel import Session

from app import clean_merge_hisa, reconcile_logs
from app.airtel import prepare_airtel_telco
from app.auth import (
    authenticate_user,
    generate_tokens,
    get_current_user,
)
from app.database import get_session
from app.glo import prepare_glo_telco
from app.mtn import prepare_mtn_telco
from app.config import BASE_DIR
from app.managers import HisaWalletManager
from app.utils import (
    compute_overall_balance,
    daterange_from_filter,
    extract_opening_closing,
    extract_user_from_wallets,
    sum_wallet_transactions,
)


app = FastAPI(
    title="Hisa Reconciliation Manager",
    max_request_size=100 * 1024 * 1024,  # 100MB limit
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Ensure the reports directory exists and mount it for static file serving.
REPORTS_DIR = os.path.join(BASE_DIR, "reports")
os.makedirs(REPORTS_DIR, exist_ok=True)
app.mount("/reports", StaticFiles(directory=REPORTS_DIR), name="reports")


@app.get("/")
def home():
    return {"message": "Welcome to Hisa Reconciliation Manager"}


@app.post("/login/", status_code=status.HTTP_200_OK)
def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: Session = Depends(get_session),
):
    user = authenticate_user(form_data.username, form_data.password, session)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    tokens = generate_tokens(data={"user": user.email, "user_id": str(user.uid)})
    return {"data": tokens}


@app.post("/reconcile_logs/")
async def reconciliation_manager(
    mno: str = Form(...),
    target_date: str = Form(...),
    hisa_file: UploadFile = File(...),
    hisa_file2: UploadFile = File(...),
    telco_file: UploadFile = File(...),
    telco_file2: UploadFile = File(None),
    use_transaction_id: bool = Form(False),
    user: dict = Depends(get_current_user),
):
    try:
        hisa_content = await hisa_file.read()
        hisa_dfs = [clean_merge_hisa(mno.upper(), "hisa1", io.BytesIO(hisa_content))]
        hisa_content2 = await hisa_file2.read()
        hisa_dfs.append(
            clean_merge_hisa(mno.upper(), "hisa2", io.BytesIO(hisa_content2))
        )
        hisa_df = pd.concat(hisa_dfs) if len(hisa_dfs) > 1 else hisa_dfs[0]

        # Process Telco files
        if mno.upper() == "MTN":
            mtn_content = await telco_file.read()
            mtn_dfs = [pd.read_excel(io.BytesIO(mtn_content))]

            if telco_file2:
                mtn_content2 = await telco_file2.read()
                mtn_dfs.append(pd.read_excel(io.BytesIO(mtn_content2)))

            mtn_df = pd.concat(mtn_dfs) if len(mtn_dfs) > 1 else mtn_dfs[0]
            cleaned_telco = prepare_mtn_telco(mtn_df)
            result = reconcile_logs(
                mno=mno.upper(),
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id,
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"],
            }
        if mno.upper() == "AIRTEL":
            airtel_content = await telco_file.read()
            airtel_dfs = [pd.read_excel(io.BytesIO(airtel_content), dtype=str)]

            if telco_file2:
                airtel_content2 = await telco_file2.read()
                airtel_dfs.append(pd.read_excel(io.BytesIO(airtel_content2), dtype=str))

            airtel_df = pd.concat(airtel_dfs) if len(airtel_dfs) > 1 else airtel_dfs[0]
            cleaned_telco = prepare_airtel_telco(airtel_df)
            result = reconcile_logs(
                mno="AIRTEL",
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id,
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"],
            }
        if mno.upper() == "GLO":
            glo_content = await telco_file.read()
            glo_dfs = [pd.read_excel(io.BytesIO(glo_content))]

            if telco_file2:
                glo_content2 = await telco_file2.read()
                glo_dfs.append(pd.read_excel(io.BytesIO(glo_content2)))

            glo_df = pd.concat(glo_dfs) if len(glo_dfs) > 1 else glo_dfs[0]
            cleaned_telco = prepare_glo_telco(glo_df)
            result = reconcile_logs(
                mno="GLO",
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id,
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"],
            }
        return JSONResponse(content=response_data)
    except Exception as e:
        return JSONResponse(
            status_code=500, content={"error": f"Error processing files: {str(e)}"}
        )


@app.get("/hisa-one/users", response_class=JSONResponse)
def get_hisa_one_users(user: dict = Depends(get_current_user)):
    handler = HisaWalletManager()
    resp = handler.fetch_users(hisa_one=True)
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/users", response_class=JSONResponse)
def get_hisa_two_users(user: dict = Depends(get_current_user)):
    handler = HisaWalletManager()
    resp = handler.fetch_users(hisa_two=True)
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/wallets", response_class=JSONResponse)
def get_hisa_one_wallets(user: dict = Depends(get_current_user)):
    handler = HisaWalletManager()
    resp = handler.fetch_wallets(hisa_one=True)
    overall = compute_overall_balance(resp)
    payload = {**resp, "overall_balance": overall}
    return JSONResponse(content=payload, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/wallets", response_class=JSONResponse)
def get_hisa_two_wallets(user: dict = Depends(get_current_user)):
    handler = HisaWalletManager()
    resp = handler.fetch_wallets(hisa_two=True)
    overall = compute_overall_balance(resp)
    payload = {**resp, "overall_balance": overall}
    return JSONResponse(content=payload, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/users/{user_id}/balances/daily", response_class=JSONResponse)
def get_hisa_one_daily_balances(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.daily_balances(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_one=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/users/{user_id}/balances/daily", response_class=JSONResponse)
def get_hisa_two_daily_balances(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.daily_balances(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_two=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/users/{user_id}/balances/monthly", response_class=JSONResponse)
def get_hisa_one_monthly_balances(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.monthly_balances(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_one=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/users/{user_id}/balances/monthly", response_class=JSONResponse)
def get_hisa_two_monthly_balances(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.monthly_balances(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_two=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/users/{user_id}/wallet-transactions", response_class=JSONResponse)
def get_hisa_one_wallet_transactions(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.wallet_transactions(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_one=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-two/users/{user_id}/wallet-transactions", response_class=JSONResponse)
def get_hisa_two_wallet_transactions(
    user_id: str,
    from_date: str,
    to_date: str,
    user: dict = Depends(get_current_user),
):
    handler = HisaWalletManager()
    resp = handler.wallet_transactions(
        user_id=user_id, from_date=from_date, to_date=to_date, hisa_two=True
    )
    return JSONResponse(content=resp, status_code=resp.get("status_code", 200))


@app.get("/hisa-one/users/{user_id}/wallet-reconciliation", response_class=JSONResponse)
def reconcile_hisa_one_user(
    user_id: str,
    filter_type: str,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    user: dict = Depends(get_current_user),
):
    start, end = daterange_from_filter(filter_type, from_date, to_date)
    handler = HisaWalletManager()

    wallets_resp = handler.fetch_wallets(hisa_one=True)
    user_wallet = extract_user_from_wallets(wallets_resp, user_id)

    daily_resp = handler.daily_balances(
        user_id=user_id, from_date=start, to_date=end, hisa_one=True
    )
    opening, closing = extract_opening_closing(daily_resp)

    trans_resp = handler.wallet_transactions(
        user_id=user_id, from_date=start, to_date=end, hisa_one=True
    )
    total_debit, total_credit = sum_wallet_transactions(trans_resp)

    expected_closing = opening + total_credit - total_debit
    difference = round(closing - expected_closing, 2)
    balanced = abs(difference) < 0.005

    payload = {
        "status_code": 200,
        "status": True,
        "data": {
            "user_id": str(user_id),
            "period": {"from": start, "to": end},
            "user": user_wallet.get("user"),
            "current_balance": user_wallet.get("current_balance", 0),
            "opening_balance": opening,
            "closing_balance": closing,
            "total_debit": total_debit,
            "total_credit": total_credit,
            "expected_closing_balance": expected_closing,
            "difference": difference,
            "balanced": balanced,
        },
        "error": None,
    }
    return JSONResponse(content=payload, status_code=200)


@app.get("/hisa-two/users/{user_id}/wallet-reconciliation", response_class=JSONResponse)
def reconcile_hisa_two_user(
    user_id: str,
    filter_type: str,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    user: dict = Depends(get_current_user),
):
    start, end = daterange_from_filter(filter_type, from_date, to_date)
    handler = HisaWalletManager()

    wallets_resp = handler.fetch_wallets(hisa_two=True)
    user_wallet = extract_user_from_wallets(wallets_resp, user_id)

    daily_resp = handler.daily_balances(
        user_id=user_id, from_date=start, to_date=end, hisa_two=True
    )
    opening, closing = extract_opening_closing(daily_resp)

    trans_resp = handler.wallet_transactions(
        user_id=user_id, from_date=start, to_date=end, hisa_two=True
    )
    total_debit, total_credit = sum_wallet_transactions(trans_resp)

    expected_closing = opening + total_credit - total_debit
    difference = round(closing - expected_closing, 2)
    balanced = abs(difference) < 0.005

    payload = {
        "status_code": 200,
        "status": True,
        "data": {
            "user_id": str(user_id),
            "period": {"from": start, "to": end},
            "user": user_wallet.get("user"),
            "current_balance": user_wallet.get("current_balance", 0),
            "opening_balance": opening,
            "closing_balance": closing,
            "total_debit": total_debit,
            "total_credit": total_credit,
            "expected_closing_balance": expected_closing,
            "difference": difference,
            "balanced": balanced,
        },
        "error": None,
    }
    return JSONResponse(content=payload, status_code=200)
