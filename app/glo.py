import pandas as pd

from app import (
    clean_merge_hisa,
    reconcile_logs,
    strip_234,
)


def prepare_glo_telco(df: pd.DataFrame):
    df = df.copy()
    expected_headers = [
        "Transaction Reference",
        "Receiver MSISDN",
        "Amount",
        "Date and Time",
    ]
    sheet_columns = [column for column in df.columns]
    retrieved_columns = [
        column for column in sheet_columns if column in expected_headers
    ]
    if len(retrieved_columns) < 4:
        return f"The following headers: {expected_headers} are expected for GLO telco."

    # Add TransactionStatus column based on Transaction or Transaction Status
    df["TransactionStatus"] = "FAILED"
    if "Transaction" in df.columns:
        df.loc[df["Transaction"].str.upper() == "SUCCESS", "TransactionStatus"] = "SUCCESS"
    if "Transaction Status" in df.columns:
        df.loc[df["Transaction Status"].str.upper() == "SUCCESS", "TransactionStatus"] = "SUCCESS"

    if df.empty:
        return "The uploaded sheet is empty."

    # GLO doesn't have a specific column for transaction type, so default to AIRTIME
    df["TransactionType"] = "AIRTIME"

    df["TxnId"] = df["Transaction Reference"].astype(str).str.replace(".", "", regex=False)
    df["MSISDN"] = df["Receiver MSISDN"].astype(str).apply(strip_234)
    df["Amount"] = pd.to_numeric(df["Amount"], errors="coerce").fillna(0).round()
    df["Date"] = pd.to_datetime(
        df["Date and Time"],
        format="%Y-%m-%d %H:%M:%S",
        errors="coerce",
    )
    df["Date"] = pd.to_datetime(df["Date"]).dt.tz_localize(None)
    df = df[["TxnId", "MSISDN", "Amount", "Date", "TransactionStatus", "TransactionType"]]
    return df.sort_values("Date")


if __name__ == "__main__":
    hisa_admin = clean_merge_hisa("GLO", "HISA ADMIN 9TH.xlsx")
    glo_telco = pd.read_excel("report (9th) (1).xlsx")
    cleaned_telco = prepare_glo_telco(glo_telco)
    result = reconcile_logs("GLO", hisa_admin, cleaned_telco, "2025-04-09")

    print("\n\n")
    print("Matching Statistics")
    print(f"{result['matching_statistics']}\n\n")
    print(f"HISA Total: {result['hisa_total']}")
    print(f"GLO Telco Total: {result['telco_total']}")
    print(f"Difference: {result['difference']}")
    print(f"\nTransactions in GLO but missing in HISA: {len(result['missing_in_hisa'])}")
    print(f"Transactions in HISA but missing in GLO: {len(result['extra_in_hisa'])}\n")
