from datetime import datetime, timedelta

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON><PERSON><PERSON>earer
import jwt
from passlib.context import CryptContext
from sqlalchemy.exc import NoResultFound
from sqlmodel import Session, select

from app.config import (
    ACCESS_TOKEN_EXPIRE_MINUTES,
    REFRESH_TOKEN_EXPIRE_MINUTES,
    SECRET_KEY,
    TIMEZONE,
)
from app.database import get_session
from app.models.user_model import User


ALGORITHM = "HS256"
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


# JWT configuration(s) here.
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def generate_tokens(data: dict):
    access_token = data.copy()
    access_expire = datetime.now(TIMEZONE) + (
        timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    access_token.update({"exp": access_expire, "token_type": "access"})
    access = jwt.encode(access_token, SECRET_KEY, algorithm=ALGORITHM)
    refresh_token = data.copy()
    refresh_expire = datetime.now(TIMEZONE) + (
        timedelta(minutes=REFRESH_TOKEN_EXPIRE_MINUTES)
    )
    refresh_token.update({"exp": refresh_expire, "token_type": "refresh"})
    refresh = jwt.encode(refresh_token, SECRET_KEY, algorithm=ALGORITHM)
    return {
        "user": data.get("user"),
        "user_id": str(data.get("user_id")),
        "access_token": access,
        "access_expiry": access_expire,
        "refresh_token": refresh,
        "refresh_expire": refresh_expire,
    }


def decode_token(token: str, token_type: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        if payload.get("token_type") != token_type:
            raise jwt.InvalidTokenError("Invalid token type.")
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None


def authenticate_user(email: str, password: str, session: Session):
    try:
        user = session.exec(
            select(User).where(User.email == email)
        ).first()
    except NoResultFound:
        return None
    if user:
        if not verify_password(password, user.hashed_password):
            return None
        return user


def get_current_user(
    token: str = Depends(oauth2_scheme),
    session: Session = Depends(get_session),
):
    payload = decode_token(token, "access")
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token.",
            headers={"WWW-Authenticate": "Bearer"},
        )
    email = payload.get("user")
    try:
        user = session.exec(
            select(User).where(User.email == email)
        ).first()
    except NoResultFound:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found.",
        )
    return user
