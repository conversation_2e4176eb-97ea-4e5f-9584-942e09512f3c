import pandas as pd

from app import (
    clean_merge_hisa,
    reconcile_logs,
    strip_234,
)


def prepare_airtel_telco(df: pd.DataFrame):
    df = df.copy()
    expected_headers = [
        "Query_reference_id",
        "Query_RECIEVER_MSISDN",
        "recTrfAmt",
        "fromDate",
    ]
    sheet_columns = [column for column in df.columns]
    retrieved_columns = [
        column for column in sheet_columns if column in expected_headers
    ]
    if len(retrieved_columns) < 4:
        return f"The following headers: {expected_headers} are expected for AIRTEL telco."

    # Add TransactionStatus column based on Query_TRANSFER_STATUS
    df["TransactionStatus"] = "FAILED"
    df.loc[df["Query_TRANSFER_STATUS"].str.upper() == "SUCCESS", "TransactionStatus"] = "SUCCESS"

    if df.empty:
        return "The uploaded sheet is empty."

    # Add TransactionType column based on Query_SUBSERVICE_NAME
    df["TransactionType"] = "AIRTIME"
    if "Query_SUBSERVICE_NAME" in df.columns:
        df.loc[df["Query_SUBSERVICE_NAME"] == "CVG", "TransactionType"] = "AIRTIME"
        df.loc[df["Query_SUBSERVICE_NAME"] == "3PP Bundle", "TransactionType"] = "DATA"

    df["TxnId"] = df["Query_reference_id"].astype(str).str.replace(".", "", regex=False)
    df["MSISDN"] = df["Query_RECIEVER_MSISDN"].astype(str).apply(strip_234)
    df["Amount"] = pd.to_numeric(df["recTrfAmt"], errors="coerce").fillna(0).round()
    df["fromDate"] = pd.to_datetime(df["fromDate"], dayfirst=True)
    df["Date"] = pd.to_datetime(df["fromDate"], format="%d/%m/%Y", errors="coerce")
    df["Date"] = df["Date"].dt.strftime("%Y-%m-%d")
    df["Date"] = pd.to_datetime(df["Date"])
    df = df[["TxnId", "MSISDN", "Amount", "Date", "TransactionStatus", "TransactionType"]]
    return df.sort_values("Date")


if __name__ == "__main__":
    hisa_admin = clean_merge_hisa("AIRTEL", "hisa11th.xlsx")
    airtel_telco = pd.read_excel("airtel11th.xlsx", dtype=str)
    cleaned_telco = prepare_airtel_telco(airtel_telco)
    result = reconcile_logs(
        "AIRTEL",
        hisa_admin,
        cleaned_telco,
        "2025-04-11",
        use_transaction_id=True,
    )

    print("\n\n")
    print("Matching Statistics")
    print(f"{result['matching_statistics']}\n\n")
    print(f"HISA Total: {result['hisa_total']}")
    print(f"AIRTEL Telco Total: {result['telco_total']}")
    print(f"Difference: {result['difference']}")
    print(f"\nTransactions in AIRTEL but missing in HISA: {len(result['missing_in_hisa'])}")
    print(f"Transactions in HISA but missing in AIRTEL: {len(result['extra_in_hisa'])}\n")
