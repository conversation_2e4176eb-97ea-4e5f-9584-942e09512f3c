from datetime import date, datetime, <PERSON>elta
import os
from pathlib import Path
from string import punctuation
from typing import Optional
from decimal import Decimal, ROUND_HALF_UP

from fastapi import HTTPException, status
import pandas as pd
from requests import exceptions, request

from app.config import BASE_DIR, API_AUTH


# reusable function(s).
def strip_234(msisdn):
    msisdn = str(msisdn)
    msisdn = msisdn.replace(".", "")
    return msisdn[-10:]


def prepare_hisa(mno: str, df: pd.DataFrame, success_value="SUCCESSFUL"):
    mno_ids = {
        "MTN": 1,
        "AIRTEL": 2,
        "GLO": 4,
    }
    mno_id = mno_ids.get(mno.upper())
    df = df.copy()
    if df.empty:
        return "The uploaded sheet is empty."

    df["TransactionStatus"] = "FAILED"
    df.loc[df["Status"].str.upper() == success_value.upper(), "TransactionStatus"] = (
        "SUCCESS"
    )
    if "Network" in df.columns:
        # Convert Network column to string type before string operations
        df["Network"] = df["Network"].astype(str)
        df = df[
            (df["Network"].str.upper() == mno.upper()) | (df["Network"] == str(mno_id))
        ]
    else:
        df["Network"] = mno.upper()
    df["TransactionType"] = "AIRTIME"
    if "Type" in df.columns:
        df.loc[df["Type"].str.upper() == "DATA", "TransactionType"] = "DATA"
    if "Txn ID" in df.columns:
        df["TxnId"] = df["Txn ID"].astype(str)
    else:
        df["TxnId"] = "NOT_SUPPLIED"

    df["MSISDN"] = df["MSISDN"].astype(str).apply(strip_234)
    df["Amount"] = df["Total Amount (₦)"].round()
    df["Date"] = pd.to_datetime(df["Date"], format="ISO8601")
    df["Date"] = pd.to_datetime(df["Date"]).dt.tz_localize(None)
    columns_to_keep = [
        "Network",
        "TxnId",
        "MSISDN",
        "Amount",
        "Date",
        "TransactionStatus",
        "TransactionType",
    ]

    if "User" in df.columns:
        df["User"] = df["User"].astype(str)
        columns_to_keep.append("User")
    else:
        df["User"] = "NOT_SUPPLIED"
        columns_to_keep.append("User")

    if "Channel" in df.columns:
        df["Channel"] = df["Channel"].astype(str)
        columns_to_keep.append("Channel")
    else:
        df["Channel"] = "NOT_SUPPLIED"
        columns_to_keep.append("Channel")

    df = df[columns_to_keep]
    return df.sort_values("Date")


def clean_merge_hisa(mno, source, hisa_admin_sheet):
    hisa_sheets = pd.ExcelFile(hisa_admin_sheet)
    available_sheets = hisa_sheets.sheet_names
    sheets = dict()
    count = 1
    for hisa_sheet in available_sheets:
        parsed_file = hisa_sheets.parse(hisa_sheet)
        cleaned_file = prepare_hisa(mno, parsed_file)
        if not isinstance(cleaned_file, str):
            cleaned_file["Source"] = source
            sheets[f"hisa{count}"] = cleaned_file
            count += 1
    if not sheets:
        return pd.DataFrame()  # Return empty DataFrame if no sheets were processed
    return pd.concat(list(sheets.values()))


def format_currency(amount: int):
    """
    Returns formatted monetary values.
    """
    if amount is None:
        currency = "NGN0"
    else:
        currency = "NGN{:,}".format(amount)
    return currency


def make_file(report_category: str, report_type: str, file_name: str):
    """
    Returns:
        - The file path & the file.
    """
    file_path = os.path.join(
        BASE_DIR, f"reports/{report_category}/{report_type}_report"
    )
    file = f"{file_path}/{file_name}"
    return {"file_path": file_path, "file": file}


def reconcile_logs(
    mno: str,
    hisa_admin: pd.DataFrame,
    telco_df: pd.DataFrame,
    target_date: str,
    use_transaction_id: Optional[bool] = False,
):
    target_date = pd.to_datetime(target_date).date()
    local = hisa_admin
    local_day = local[local["Date"].dt.date == target_date].copy()
    telco_clean = telco_df

    # Check if telco_clean is a string (error message) or DataFrame
    if isinstance(telco_clean, str):
        raise ValueError(f"Error processing telco data: {telco_clean}")

    telco_day = telco_clean[telco_clean["Date"].dt.date == target_date].copy()

    # Calculate total counts and values for all transactions (successful and failed)
    hisa_all_count = len(local_day)
    hisa_all_value = local_day["Amount"].sum()
    telco_all_count = len(telco_day)
    telco_all_value = telco_day["Amount"].sum()

    # Calculate counts and values by transaction type (AIRTIME and DATA)
    hisa_airtime_count = len(local_day[local_day["TransactionType"] == "AIRTIME"])
    hisa_airtime_value = local_day[local_day["TransactionType"] == "AIRTIME"][
        "Amount"
    ].sum()
    hisa_data_count = len(local_day[local_day["TransactionType"] == "DATA"])
    hisa_data_value = local_day[local_day["TransactionType"] == "DATA"]["Amount"].sum()

    telco_airtime_count = len(telco_day[telco_day["TransactionType"] == "AIRTIME"])
    telco_airtime_value = telco_day[telco_day["TransactionType"] == "AIRTIME"][
        "Amount"
    ].sum()
    telco_data_count = len(telco_day[telco_day["TransactionType"] == "DATA"])
    telco_data_value = telco_day[telco_day["TransactionType"] == "DATA"]["Amount"].sum()

    # Calculate counts and values for failed transactions
    hisa_failed_count = len(local_day[local_day["TransactionStatus"] == "FAILED"])
    hisa_failed_value = local_day[local_day["TransactionStatus"] == "FAILED"][
        "Amount"
    ].sum()
    telco_failed_count = len(telco_day[telco_day["TransactionStatus"] == "FAILED"])
    telco_failed_value = telco_day[telco_day["TransactionStatus"] == "FAILED"][
        "Amount"
    ].sum()

    # Filter for successful transactions for reconciliation
    local_day_success = local_day[local_day["TransactionStatus"] == "SUCCESS"].copy()
    telco_day_success = telco_day[telco_day["TransactionStatus"] == "SUCCESS"].copy()

    # Calculate successful transactions by type
    hisa_success_airtime_count = len(
        local_day_success[local_day_success["TransactionType"] == "AIRTIME"]
    )
    hisa_success_airtime_value = local_day_success[
        local_day_success["TransactionType"] == "AIRTIME"
    ]["Amount"].sum()
    hisa_success_data_count = len(
        local_day_success[local_day_success["TransactionType"] == "DATA"]
    )
    hisa_success_data_value = local_day_success[
        local_day_success["TransactionType"] == "DATA"
    ]["Amount"].sum()

    telco_success_airtime_count = len(
        telco_day_success[telco_day_success["TransactionType"] == "AIRTIME"]
    )
    telco_success_airtime_value = telco_day_success[
        telco_day_success["TransactionType"] == "AIRTIME"
    ]["Amount"].sum()
    telco_success_data_count = len(
        telco_day_success[telco_day_success["TransactionType"] == "DATA"]
    )
    telco_success_data_value = telco_day_success[
        telco_day_success["TransactionType"] == "DATA"
    ]["Amount"].sum()

    # print(f"LOCAL DAY (All): {hisa_all_count}")
    # print(f"LOCAL DAY (Success): {len(local_day_success)}")
    # print(f"TELCO DAY (All): {telco_all_count}")
    # print(f"TELCO DAY (Success): {len(telco_day_success)}\n")

    # Create match keys including transaction type when applicable
    if use_transaction_id:
        if mno.upper() in ["MTN", "AIRTEL"]:
            # Include TransactionType in match key for MTN and AIRTEL
            local_day_success["match_key"] = (
                local_day_success["TxnId"]
                + "_"
                + local_day_success["MSISDN"]
                + "_"
                + local_day_success["Amount"].astype(str)
                + "_"
                + local_day_success["TransactionType"]
            )
            telco_day_success["match_key"] = (
                telco_day_success["TxnId"]
                + "_"
                + telco_day_success["MSISDN"]
                + "_"
                + telco_day_success["Amount"].astype(str)
                + "_"
                + telco_day_success["TransactionType"]
            )
        else:
            # For other MNOs, use the original match key
            local_day_success["match_key"] = (
                local_day_success["TxnId"]
                + "_"
                + local_day_success["MSISDN"]
                + "_"
                + local_day_success["Amount"].astype(str)
            )
            telco_day_success["match_key"] = (
                telco_day_success["TxnId"]
                + "_"
                + telco_day_success["MSISDN"]
                + "_"
                + telco_day_success["Amount"].astype(str)
            )
    else:
        if mno.upper() in ["MTN", "AIRTEL"]:
            # Include TransactionType in match key for MTN and AIRTEL
            local_day_success["match_key"] = (
                local_day_success["MSISDN"]
                + "_"
                + local_day_success["Amount"].astype(str)
                + "_"
                + local_day_success["TransactionType"]
            )
            telco_day_success["match_key"] = (
                telco_day_success["MSISDN"]
                + "_"
                + telco_day_success["Amount"].astype(str)
                + "_"
                + telco_day_success["TransactionType"]
            )
        else:
            # For other MNOs, use the original match key
            local_day_success["match_key"] = (
                local_day_success["MSISDN"]
                + "_"
                + local_day_success["Amount"].astype(str)
            )
            telco_day_success["match_key"] = (
                telco_day_success["MSISDN"]
                + "_"
                + telco_day_success["Amount"].astype(str)
            )

    # Find duplicates in local_day (HISA)
    local_duplicates = local_day_success[
        local_day_success.duplicated("match_key", keep=False)
    ].copy()

    # Find duplicates in telco_day
    telco_duplicates = telco_day_success[
        telco_day_success.duplicated("match_key", keep=False)
    ].copy()

    local_keys = set(local_day_success["match_key"])
    telco_keys = set(telco_day_success["match_key"])
    missing_keys = telco_keys - local_keys
    extra_keys = local_keys - telco_keys
    matching_keys = local_keys & telco_keys

    # Get missing and extra records
    # For telco records missing in HISA
    telco_columns = ["TxnId", "MSISDN", "Amount", "Date", "TransactionType"]
    missing_in_hisa = telco_day_success[
        telco_day_success["match_key"].isin(missing_keys)
    ][telco_columns]

    # For HISA records not found in telco, include User and Channel columns if they exist
    hisa_columns = ["TxnId", "MSISDN", "Amount", "Date", "TransactionType"]
    if "User" in local_day_success.columns:
        hisa_columns.append("User")
    if "Channel" in local_day_success.columns:
        hisa_columns.append("Channel")
    extra_in_hisa = local_day_success[local_day_success["match_key"].isin(extra_keys)][
        hisa_columns
    ]

    # write duplicate records to file.
    file_maker = make_file(
        report_category=f"{mno}_reconciliation",
        report_type="duplicates",
        file_name=f"{mno}_duplicates_{target_date}.xlsx",
    )
    file_path = file_maker.get("file_path")
    duplicates_file = file_maker.get("file")
    if not os.path.exists(file_path):
        directory = Path(file_path)
        directory.mkdir(parents=True, exist_ok=True)
    with pd.ExcelWriter(duplicates_file) as writer:
        if not local_duplicates.empty:
            local_duplicates.to_excel(writer, sheet_name="HISA_Duplicates", index=False)
        if not telco_duplicates.empty:
            telco_duplicates.to_excel(
                writer, sheet_name=f"{mno}_Duplicates", index=False
            )
        # Add empty sheet if no data to write
        if local_duplicates.empty and telco_duplicates.empty:
            pd.DataFrame({"Message": ["No duplicate records found"]}).to_excel(
                writer, sheet_name="No_Duplicates", index=False
            )

    # write missing records to file.
    file_maker = make_file(
        report_category=f"{mno}_reconciliation",
        report_type="missing",
        file_name=f"{mno}_missing_{target_date}.xlsx",
    )
    file_path = file_maker.get("file_path")
    missing_file = file_maker.get("file")
    if not os.path.exists(file_path):
        directory = Path(file_path)
        directory.mkdir(parents=True, exist_ok=True)
    with pd.ExcelWriter(missing_file) as writer:
        if not extra_in_hisa.empty:
            extra_in_hisa.to_excel(writer, sheet_name="HISA_Missing", index=False)
        if not missing_in_hisa.empty:
            missing_in_hisa.to_excel(writer, sheet_name=f"{mno}_Missing", index=False)
        # Add empty sheet if no data to write
        if extra_in_hisa.empty and missing_in_hisa.empty:
            pd.DataFrame({"Message": ["No missing records found"]}).to_excel(
                writer, sheet_name="No_Missing", index=False
            )

    # Generate user consumption report
    user_consumption_file = generate_user_consumption_report(
        local_day, mno, target_date
    )

    # Calculate success-only values for reconciliation
    hisa_success_value = local_day_success["Amount"].sum()
    telco_success_value = telco_day_success["Amount"].sum()

    return {
        "hisa_all_count": hisa_all_count,
        "hisa_all_value": format_currency(hisa_all_value),
        "telco_all_count": telco_all_count,
        "telco_all_value": format_currency(telco_all_value),
        "hisa_success_count": len(local_day_success),
        "telco_success_count": len(telco_day_success),
        "matching_statistics": {
            "total_unique_in_HISA": len(local_keys),
            "total_unique_in_TELCO": len(telco_keys),
            "matching_transactions": len(matching_keys),
            "match_combination_keys_in_HISA_but_not_in_TELCO": len(
                local_keys - telco_keys
            ),
            "match_combination_keys_in_TELCO_but_not_in_HISA": len(
                telco_keys - local_keys
            ),
        },
        "hisa_total": format_currency(hisa_success_value),
        "telco_total": format_currency(telco_success_value),
        "difference": format_currency(telco_success_value - hisa_success_value),
        "missing_in_hisa": missing_in_hisa,
        "extra_in_hisa": extra_in_hisa,
        "local_duplicates": local_duplicates,
        "telco_duplicates": telco_duplicates,
        "duplicates_file": duplicates_file,
        "missing_file": missing_file,
        "user_consumption_file": user_consumption_file,
        # New metrics
        "hisa_airtime_count": hisa_airtime_count,
        "hisa_airtime_value": format_currency(hisa_airtime_value),
        "hisa_data_count": hisa_data_count,
        "hisa_data_value": format_currency(hisa_data_value),
        "telco_airtime_count": telco_airtime_count,
        "telco_airtime_value": format_currency(telco_airtime_value),
        "telco_data_count": telco_data_count,
        "telco_data_value": format_currency(telco_data_value),
        "hisa_failed_count": hisa_failed_count,
        "hisa_failed_value": format_currency(hisa_failed_value),
        "telco_failed_count": telco_failed_count,
        "telco_failed_value": format_currency(telco_failed_value),
        "hisa_success_airtime_count": hisa_success_airtime_count,
        "hisa_success_airtime_value": format_currency(hisa_success_airtime_value),
        "hisa_success_data_count": hisa_success_data_count,
        "hisa_success_data_value": format_currency(hisa_success_data_value),
        "telco_success_airtime_count": telco_success_airtime_count,
        "telco_success_airtime_value": format_currency(telco_success_airtime_value),
        "telco_success_data_count": telco_success_data_count,
        "telco_success_data_value": format_currency(telco_success_data_value),
    }


def get_balances_by_date(balance_records: list, target_date: str):
    """ """
    for record in balance_records:
        if record["date"] == target_date.isoformat():
            return {
                "opening_balance": record["opening_balance"],
                "closing_balance": record["closing_balance"],
            }
    return {"opening_balance": 0, "closing_balance": 0}


def balances_api(source: str, user_id: str, target_date: str):
    """
    Retrieves the opening and closing balances for a given user on a specific date.
    Args:
        source (str): The source system (hisa1 or hisa2)
        user_id (str): The user ID for which balances are to be retrieved
        target_date (str): The target date for which the balances are to be retrieved.
    Returns:
        dict: A dictionary containing the opening and closing balances for the specified user and date.
        The dictionary has the following structure:
        {
            "opening_balance": int,
            "closing_balance": int
        }
    """
    hisa1_url = f"https://api.myhisa.com/api/third-party/services/reports/statement/daily?from={target_date}&to={target_date}&user_id={user_id}"
    hisa2_url = f"http://94.229.72.28:3000/api/third-party/services/reports/statement/daily?from_date={target_date}&to_date={target_date}&user_id={user_id}"

    if source == "hisa1":
        url = hisa1_url
        headers = {"api-auth": API_AUTH}
    elif source == "hisa2":
        url = hisa2_url
        headers = {"Authorization": API_AUTH}
    else:
        return {"opening_balance": 0, "closing_balance": 0}

    payload = {}
    try:
        response = request("GET", url, headers=headers, data=payload)
        if response.status_code == 200:
            try:
                response_data = response.json()
                response_data = _convert_kobo_fields(response_data)
                if response_data.get("status"):
                    balance_records = response_data.get("data", [])
                    if balance_records:
                        return get_balances_by_date(balance_records, target_date)
                    else:
                        return {"opening_balance": 0, "closing_balance": 0}
                else:
                    return {"opening_balance": 0, "closing_balance": 0}
            except exceptions.JSONDecodeError as error:
                print(f"Error decoding JSON response for user {user_id}: {error}")
                return {"opening_balance": 0, "closing_balance": 0}
        else:
            print(
                f"API request failed for user {user_id} with status code: {response.status_code}"
            )
            return {"opening_balance": 0, "closing_balance": 0}
    except exceptions.RequestException as error:
        print(f"Error fetching balances for user {user_id}: {error}")
        return {"opening_balance": 0, "closing_balance": 0}


def create_wallet_summary(df, target_date):
    """
    Creates a wallet summary showing total spending by user with opening and closing balances.

    Args:
        df (pd.DataFrame): DataFrame containing HISA transactions
        target_date (str): Target date for balance retrieval
    Returns:
        pd.DataFrame: Wallet summary with balance information
    """
    try:
        # Group by User and Source to get total spending by transaction type
        user_summary = (
            df.groupby(["User", "Source"]).agg({"Amount": "sum"}).reset_index()
        )

        # Separate airtime and data spending
        airtime_spending = (
            df[df["TransactionType"] == "AIRTIME"]
            .groupby(["User", "Source"])
            .agg({"Amount": "sum"})
            .reset_index()
        )
        airtime_spending.columns = ["User", "Source", "AirtimeSpent"]

        data_spending = (
            df[df["TransactionType"] == "DATA"]
            .groupby(["User", "Source"])
            .agg({"Amount": "sum"})
            .reset_index()
        )
        data_spending.columns = ["User", "Source", "DataSpent"]

        # Merge spending data
        wallet_summary = user_summary.merge(
            airtime_spending, on=["User", "Source"], how="left"
        )
        wallet_summary = wallet_summary.merge(
            data_spending, on=["User", "Source"], how="left"
        )

        # Fill NaN values with 0
        wallet_summary["AirtimeSpent"] = wallet_summary["AirtimeSpent"].fillna(0)
        wallet_summary["DataSpent"] = wallet_summary["DataSpent"].fillna(0)

        # Rename Amount column to TotalSpent
        wallet_summary.rename(columns={"Amount": "TotalSpent"}, inplace=True)

        # Add balance information for each user
        balance_data = []
        for _, row in wallet_summary.iterrows():
            user_id = row["User"]
            source = row["Source"]

            # Skip if user is "Unknown" or "NOT_SUPPLIED"
            if user_id in ["Unknown", "NOT_SUPPLIED"]:
                balance_info = {"opening_balance": 0, "closing_balance": 0}
            else:
                try:
                    balance_info = balances_api(source, user_id, target_date)
                    if balance_info is None:
                        balance_info = {"opening_balance": 0, "closing_balance": 0}
                except Exception as e:
                    print(
                        f"Error fetching balance for user {user_id} from {source}: {e}"
                    )
                    balance_info = {"opening_balance": 0, "closing_balance": 0}

            balance_data.append(balance_info)

        # Add balance columns
        balance_df = pd.DataFrame(balance_data)
        wallet_summary["OpeningBalance"] = balance_df["opening_balance"]
        wallet_summary["ClosingBalance"] = balance_df["closing_balance"]

        # Reorder columns for better readability
        column_order = [
            "User",
            "Source",
            "TotalSpent",
            "AirtimeSpent",
            "DataSpent",
            "OpeningBalance",
            "ClosingBalance",
        ]
        wallet_summary = wallet_summary[column_order]
        return wallet_summary
    except Exception as e:
        print(f"Error creating wallet summary: {e}")
        return pd.DataFrame()


def generate_user_consumption_report(local_day, mno, target_date):
    """
    Generates a report showing the amount consumed by each user based on transaction type,
    including wallet summary with opening and closing balances.

    Args:
        local_day (pd.DataFrame): DataFrame containing HISA transactions for the target date
        mno (str): Mobile Network Operator name
        target_date (str): Target date for the report
    Returns:
        str: Path to the generated report file
    """
    # Create a copy of the DataFrame to avoid modifying the original
    df = local_day.copy()

    # Check if User column exists
    if "User" not in df.columns:
        df["User"] = "Unknown"

    # Group by User and TransactionType to calculate total amount
    user_consumption = (
        df.groupby(["User", "TransactionType", "Source"])
        .agg(
            {
                "Amount": ["sum", "count"],
                "TransactionStatus": lambda x: (
                    x == "SUCCESS"
                ).sum(),  # Count successful transactions
            }
        )
        .reset_index()
    )
    user_consumption.columns = [
        "User",
        "TransactionType",
        "Source",
        "TotalAmount",
        "TransactionCount",
        "SuccessfulTransactions",
    ]
    user_consumption["SuccessRate"] = (
        user_consumption["SuccessfulTransactions"]
        / user_consumption["TransactionCount"]
        * 100
    ).round(2)

    # Create wallet summary with balance information
    wallet_summary = create_wallet_summary(df, target_date)

    file_maker = make_file(
        report_category=f"{mno}_reconciliation",
        report_type="user_consumption",
        file_name=f"{mno}_user_consumption_{target_date}.xlsx",
    )
    file_path = file_maker.get("file_path")
    report_file = file_maker.get("file")

    if not os.path.exists(file_path):
        directory = Path(file_path)
        directory.mkdir(parents=True, exist_ok=True)

    # Write to Excel with multiple sheets
    with pd.ExcelWriter(report_file) as writer:
        # Summary sheet with all data
        user_consumption.to_excel(writer, sheet_name="Summary", index=False)

        # Wallet Summary sheet with balance information
        if not wallet_summary.empty:
            wallet_summary.to_excel(writer, sheet_name="Wallet_Summary", index=False)

        # Create separate sheets for each transaction type
        for tx_type in user_consumption["TransactionType"].unique():
            type_data = user_consumption[user_consumption["TransactionType"] == tx_type]
            type_data.to_excel(
                writer, sheet_name=f"{tx_type}_Transactions", index=False
            )

        # Create separate sheets for each source (hisa1, hisa2)
        for source in user_consumption["Source"].unique():
            source_data = user_consumption[user_consumption["Source"] == source]
            source_data.to_excel(writer, sheet_name=f"{source}_Data", index=False)

    return report_file


def validate_password(password: str):
    """
    Validates the given password based on the criteria.
    Args:
        password (str): The password string to be validated.
    Raises:
        HTTPException:
        if the password fails to meet any of the following criteria:
        - The length of the password is less than 8 characters.
        - The password does not contain at least one numeric digit.
        - The password does not contain at least one uppercase character.
        - The password does not contain at least one lowercase character.
        - The password does not contain at least one special character.
    Returns:
        bool: True if the password passes all validation criteria.
    """
    special_characters = list(punctuation)

    if len(password) < 8:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password length cannot be less than 8 characters.",
        )
    if not any(char.isdigit() for char in password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password should have at least one numeric digit.",
        )
    if not any(char.isupper() for char in password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password should have at least one uppercase character.",
        )
    if not any(char.islower() for char in password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password should have at least one lowercase character.",
        )
    if not any(char in special_characters for char in password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password should have at least one special character.",
        )
    return True


# Helpers for Kobo -> Naira conversion on API responses
_MONEY_KEYS = {
    "amount",
    "opening_balance",
    "closing_balance",
    "debit_amount",
    "credit_amount",
    "total_amount",
}


def _safe_int(val, default: int = 0) -> int:
    try:
        return int(str(val).replace(",", ""))
    except Exception:
        try:
            return int(float(val))
        except Exception:
            return default


def convert_kobo_to_naira(val):
    """Convert a kobo value (possibly string/number) to Naira with Kobo (2 dp)."""
    try:
        d = Decimal(str(val).replace(",", ""))
    except Exception:
        try:
            d = Decimal(float(val))
        except Exception:
            d = Decimal(0)
    naira = (d / Decimal(100)).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
    return float(naira)


def _convert_kobo_fields(obj):
    """
    Recursively walk a JSON-like structure (dict/list) and convert monetary
    fields expressed in Kobo to Naira by dividing by 100 for known keys.
    """
    if isinstance(obj, dict):
        new_obj = {}
        for k, v in obj.items():
            if k in _MONEY_KEYS:
                new_obj[k] = convert_kobo_to_naira(v)
            else:
                new_obj[k] = _convert_kobo_fields(v)
        return new_obj
    elif isinstance(obj, list):
        return [_convert_kobo_fields(x) for x in obj]
    else:
        return obj


def make_request(request_type: str, params: dict) -> dict:
    """
    Make an HTTP request using the specified request_type and parameters.
    Args:
        request_type (str): The type of HTTP request to make (e.g., 'GET', 'POST', 'PUT', 'DELETE', etc.).
        params (dict): A dictionary containing the parameters to be passed in the HTTP request.
    Returns:
        dict: A dictionary containing the response status, data, and error details.
        - 'status': A boolean indicating if the request was successful (True) or not (False).
        - 'data': A dictionary containing the JSON response data if the request was successful, otherwise None.
        - 'error': A dictionary containing error details if the request failed, otherwise None.
    """
    try:
        response = request(request_type, **params)
        try:
            data = response.json()
            data = _convert_kobo_fields(data)
        except exceptions.JSONDecodeError:
            data = response.text
        return {
            "status_code": response.status_code,
            "status": True,
            "data": data,
            "error": None,
        }
    except exceptions.RequestException as error:
        return {
            "status_code": 500,
            "status": False,
            "data": None,
            "error": str(error),
        }


# Helper to compute overall balance from wallets response
# Returns a float naira amount with 2 decimals


def compute_overall_balance(response_data) -> float:
    try:
        data = response_data.get("data")
        wallets = []
        if isinstance(data, dict):
            inner = data.get("data")
            if isinstance(inner, list):
                wallets = inner
            elif isinstance(data, list):
                wallets = data
        elif isinstance(data, list):
            wallets = data
        total = Decimal("0.00")
        for item in wallets:
            amt = item.get("amount", 0.0)
            try:
                total += Decimal(str(amt))
            except Exception:
                try:
                    total += Decimal(float(amt))
                except Exception:
                    continue
        return float(total.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))
    except Exception:
        return 0.0


def parse_int(val, default: int = 0) -> int:
    try:
        # handle numbers with commas or strings
        return int(str(val).replace(",", ""))
    except Exception:
        try:
            return int(float(val))
        except Exception:
            return default


def parse_money(val, default: float = 0.0) -> float:
    """Parse a value into a Naira float with 2 decimal places."""
    try:
        d = Decimal(str(val).replace(",", ""))
    except Exception:
        try:
            d = Decimal(float(val))
        except Exception:
            return default
    return float(d.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))


def get_user_transaction_summary(
    user_id: str,
    csv_file_path: str = "reports/hisa_logs/2025-09-09/2025-09-09_transactions.csv",
):
    """
    Analyzes transaction data for a specific user and returns count and value by transaction status.

    This function reads transaction data from a CSV file and provides detailed analytics
    for a specific user, including breakdowns by transaction status and type.

    Args:
        user_id (str): The user ID to analyze transactions for
        csv_file_path (str): Path to the CSV file containing transaction data.
                           Defaults to the 2025-09-09 transactions file.

    Returns:
        dict: A dictionary containing transaction statistics by status. Example:
        {
            "user_id": "5",
            "total_transactions": 14273,
            "total_value_naira": 19675657.00,
            "by_status": {
                "Successful": {"count": 14100, "value_naira": 19316902.00},
                "Failed": {"count": 91, "value_naira": 243855.00},
                "InvalidMsisdn": {"count": 79, "value_naira": 112700.00},
                "Pending": {"count": 3, "value_naira": 2200.00}
            },
            "by_type": {
                "Airtime": {"count": 14273, "value_naira": 19675657.00}
            }
        }

        If no transactions are found for the user:
        {
            "user_id": "99999",
            "total_transactions": 0,
            "total_value_naira": 0.0,
            "by_status": {},
            "by_type": {},
            "message": "No transactions found for user_id: 99999"
        }

        If an error occurs:
        {
            "user_id": "5",
            "error": "Error message describing what went wrong"
        }

    Example Usage:
        # Analyze transactions for user ID 5
        result = get_user_transaction_summary("5")

        if "error" not in result:
            print(f"User {result['user_id']} has {result['total_transactions']} transactions")
            print(f"Total value: ₦{result['total_value_naira']:,.2f}")

            # Show successful transactions
            if "Successful" in result['by_status']:
                successful = result['by_status']['Successful']
                print(f"Successful: {successful['count']} transactions worth ₦{successful['value_naira']:,.2f}")
    """
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file_path)

        # Filter transactions for the specific user
        user_transactions = df[df["user_id"].astype(str) == str(user_id)].copy()

        if user_transactions.empty:
            return {
                "user_id": str(user_id),
                "total_transactions": 0,
                "total_value_naira": 0.0,
                "by_status": {},
                "by_type": {},
                "message": f"No transactions found for user_id: {user_id}",
            }

        # Convert amount from kobo to naira (divide by 100)
        user_transactions["amount_naira"] = user_transactions["amount"] / 100.0

        # Calculate overall statistics
        total_transactions = len(user_transactions)
        total_value_naira = user_transactions["amount_naira"].sum()

        # Group by status
        status_summary = (
            user_transactions.groupby("status")
            .agg({"amount_naira": ["count", "sum"]})
            .round(2)
        )
        status_summary.columns = ["count", "value_naira"]
        status_summary = status_summary.reset_index()

        # Convert status summary to dictionary
        by_status = {}
        for _, row in status_summary.iterrows():
            by_status[row["status"]] = {
                "count": int(row["count"]),
                "value_naira": float(row["value_naira"]),
            }

        # Group by transaction type
        type_summary = (
            user_transactions.groupby("type")
            .agg({"amount_naira": ["count", "sum"]})
            .round(2)
        )
        type_summary.columns = ["count", "value_naira"]
        type_summary = type_summary.reset_index()

        # Convert type summary to dictionary
        by_type = {}
        for _, row in type_summary.iterrows():
            by_type[row["type"]] = {
                "count": int(row["count"]),
                "value_naira": float(row["value_naira"]),
            }

        return {
            "user_id": str(user_id),
            "total_transactions": total_transactions,
            "total_value_naira": round(total_value_naira, 2),
            "by_status": by_status,
            "by_type": by_type,
        }

    except FileNotFoundError:
        return {
            "user_id": str(user_id),
            "error": f"CSV file not found: {csv_file_path}",
        }
    except Exception as e:
        return {
            "user_id": str(user_id),
            "error": f"Error processing transactions: {str(e)}",
        }


def daterange_from_filter(
    filter_type: str,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
):
    ft = (filter_type or "").lower().strip()
    today_d = date.today()
    if ft == "today":
        start, end = today_d, today_d
    elif ft == "yesterday":
        d = today_d - timedelta(days=1)
        start, end = d, d
    elif ft == "current_week":
        start = today_d - timedelta(days=today_d.weekday())
        end = today_d
    elif ft == "last_week":
        end = today_d - timedelta(days=today_d.weekday() + 1)
        start = end - timedelta(days=6)
    elif ft == "current_month":
        start = today_d.replace(day=1)
        end = today_d
    elif ft == "last_month":
        first_this = today_d.replace(day=1)
        end = first_this - timedelta(days=1)
        start = end.replace(day=1)
    elif ft in ("current_quarter", "current_qaurter"):
        q_start_month = ((today_d.month - 1) // 3) * 3 + 1
        start = date(today_d.year, q_start_month, 1)
        end = today_d
    elif ft == "last_quarter":
        curr_q_start_month = ((today_d.month - 1) // 3) * 3 + 1
        curr_q_start = date(today_d.year, curr_q_start_month, 1)
        last_q_end = curr_q_start - timedelta(days=1)
        last_q_start_month = ((last_q_end.month - 1) // 3) * 3 + 1
        start = date(last_q_end.year, last_q_start_month, 1)
        end = last_q_end
    elif ft == "custom":
        if not from_date or not to_date:
            raise HTTPException(
                status_code=400,
                detail="from_date and to_date are required for custom filter_type",
            )
        start = datetime.fromisoformat(from_date).date()
        end = datetime.fromisoformat(to_date).date()
    else:
        raise HTTPException(status_code=400, detail="Invalid filter_type")
    return start.isoformat(), end.isoformat()


def extract_user_from_wallets(wallets_resp: dict, target_user_id: str):
    data = wallets_resp.get("data")
    items = []
    if isinstance(data, dict) and isinstance(data.get("data"), list):
        items = data.get("data")
    elif isinstance(data, list):
        items = data
    # Find by wallet.user_id or wallet.user.id
    for w in items:
        uid = str(w.get("user_id")) if w.get("user_id") is not None else None
        if uid is None and isinstance(w.get("user"), dict):
            uid = str(w["user"].get("id")) if w["user"].get("id") is not None else None
        if uid is not None and uid == str(target_user_id):
            return {
                "current_balance": parse_money(w.get("amount", 0.0), 0.0),
                "user": w.get("user", {"id": uid}),
                "wallet": {k: v for k, v in w.items() if k != "user"},
            }
    return {"current_balance": 0, "user": None, "wallet": None}


def extract_opening_closing(daily_resp: dict):
    data = daily_resp.get("data")
    records = []
    if isinstance(data, dict) and isinstance(data.get("data"), list):
        records = data.get("data")
    elif isinstance(data, list):
        records = data
    if not records:
        return 0, 0
    try:
        records = sorted(records, key=lambda r: r.get("date", ""))
    except Exception:
        pass
    opening = parse_money(records[0].get("opening_balance", 0.0), 0.0)
    closing = parse_money(records[-1].get("closing_balance", 0.0), 0.0)
    return opening, closing


def sum_wallet_transactions(trans_resp: dict):
    data = trans_resp.get("data")
    records = []
    if isinstance(data, dict) and isinstance(data.get("data"), list):
        records = data.get("data")
    elif isinstance(data, list):
        records = data
    debit = 0.0
    credit = 0.0
    for r in records:
        t = (r.get("type") or "").lower()
        amt = parse_money(r.get("total_amount", 0.0), 0.0)
        if t == "debit":
            debit += amt
        elif t == "credit":
            credit += amt
    return debit, credit
