#!/usr/bin/env python3
"""
Test script for the wallet transactions analysis functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from utils import get_user_wallet_transactions_summary

def test_wallet_transactions_analysis():
    """Test the wallet transactions analysis function."""
    print("🧪 Testing Wallet Transactions Analysis")
    print("=" * 60)
    
    # Test with user ID 5 (known to have transactions)
    user_id = "5"
    csv_file_path = "reports/hisa_logs/2025-09-09/2025-09-09_wallet_transactions.csv"
    
    print(f"Analyzing wallet transactions for user ID: {user_id}")
    print(f"Using CSV file: {csv_file_path}")
    print("-" * 60)
    
    try:
        result = get_user_wallet_transactions_summary(user_id, csv_file_path)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return
        
        print("✅ Analysis completed successfully!")
        print()
        
        # Display summary
        print("📊 WALLET TRANSACTION SUMMARY")
        print("=" * 40)
        print(f"User ID: {result['user_id']}")
        print(f"Total Wallet Transactions: {result['total_transactions']:,}")
        print()
        
        print("💰 CREDITS & DEBITS")
        print("-" * 20)
        print(f"Total Credits: ₦{result['total_credits']:,.2f} ({result['credit_count']:,} transactions)")
        print(f"Total Debits:  ₦{result['total_debits']:,.2f} ({result['debit_count']:,} transactions)")
        print(f"Net Change:    ₦{result['net_change']:,.2f}")
        print()
        
        # Show breakdown by action
        print("📋 BREAKDOWN BY ACTION")
        print("-" * 25)
        for action, data in result['by_action'].items():
            print(f"{action.capitalize()}: {data['count']:,} transactions, ₦{data['value_naira']:,.2f}")
        
        print()
        print("🎯 ANALYSIS COMPLETE")
        
        # Test with different user IDs
        print("\n" + "=" * 60)
        print("Testing with different user IDs...")
        
        test_user_ids = ["1", "10", "20", "999"]  # Mix of existing and non-existing users
        
        for test_user_id in test_user_ids:
            print(f"\nTesting user ID: {test_user_id}")
            test_result = get_user_wallet_transactions_summary(test_user_id, csv_file_path)
            
            if "error" in test_result:
                print(f"  ❌ Error: {test_result['error']}")
            else:
                print(f"  ✅ Found {test_result['total_transactions']:,} wallet transactions")
                if test_result['total_transactions'] > 0:
                    print(f"     Credits: ₦{test_result['total_credits']:,.2f}, Debits: ₦{test_result['total_debits']:,.2f}")
                    print(f"     Net: ₦{test_result['net_change']:,.2f}")
        
    except Exception as e:
        print(f"❌ Exception occurred: {e}")

def test_error_handling():
    """Test error handling for missing files and invalid data."""
    print("\n" + "=" * 60)
    print("🧪 Testing Error Handling")
    print("=" * 60)
    
    # Test with non-existent file
    print("Testing with non-existent file...")
    result = get_user_wallet_transactions_summary("5", "nonexistent_file.csv")
    if "error" in result:
        print(f"✅ Correctly handled missing file: {result['error']}")
    else:
        print("❌ Should have returned error for missing file")
    
    print("\nError handling tests completed!")

def compare_with_transaction_analysis():
    """Compare wallet transactions with regular transactions for consistency."""
    print("\n" + "=" * 60)
    print("🧪 Comparing with Transaction Analysis")
    print("=" * 60)
    
    from utils import get_user_transaction_summary
    
    user_id = "5"
    
    # Get regular transaction summary
    transaction_result = get_user_transaction_summary(
        user_id, 
        "reports/hisa_logs/2025-09-09/2025-09-09_transactions.csv"
    )
    
    # Get wallet transaction summary
    wallet_result = get_user_wallet_transactions_summary(
        user_id,
        "reports/hisa_logs/2025-09-09/2025-09-09_wallet_transactions.csv"
    )
    
    if "error" not in transaction_result and "error" not in wallet_result:
        print("📊 COMPARISON RESULTS")
        print("-" * 30)
        print(f"Regular Transactions: {transaction_result['total_transactions']:,}")
        print(f"Wallet Transactions:  {wallet_result['total_transactions']:,}")
        print()
        
        successful_value = transaction_result.get('by_status', {}).get('Successful', {}).get('value_naira', 0)
        wallet_debits = wallet_result.get('total_debits', 0)
        
        print(f"Successful Transaction Value: ₦{successful_value:,.2f}")
        print(f"Total Wallet Debits:         ₦{wallet_debits:,.2f}")
        print()
        
        if abs(successful_value - wallet_debits) < 1:  # Allow small rounding differences
            print("✅ Values match closely - good consistency!")
        else:
            difference = abs(successful_value - wallet_debits)
            print(f"⚠️  Values differ by ₦{difference:,.2f} - may need investigation")
    else:
        print("❌ Could not compare due to errors in one or both analyses")

if __name__ == "__main__":
    test_wallet_transactions_analysis()
    test_error_handling()
    compare_with_transaction_analysis()
    
    print("\n" + "=" * 60)
    print("🎉 All tests completed!")
    print("=" * 60)
