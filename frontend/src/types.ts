export interface ReconciliationResult {
  hisa_all_count: number;
  hisa_all_value: string;
  telco_all_count: number;
  telco_all_value: string;
  hisa_success_count: number;
  telco_success_count: number;
  matching_statistics: Record<string, number>;
  hisa_total: string;
  telco_total: string;
  difference: string;
  missing_in_hisa_count: number;
  extra_in_hisa_count: number;
  local_duplicates_count: number;
  telco_duplicates_count: number;
  duplicates_file_url: string;
  missing_file_url: string;
  user_consumption_file_url: string;
  hisa_airtime_count: number;
  hisa_airtime_value: string;
  hisa_data_count: number;
  hisa_data_value: string;
  telco_airtime_count: number;
  telco_airtime_value: string;
  telco_data_count: number;
  telco_data_value: string;
  hisa_failed_count: number;
  hisa_failed_value: string;
  telco_failed_count: number;
  telco_failed_value: string;
  hisa_success_airtime_count: number;
  hisa_success_airtime_value: string;
  hisa_success_data_count: number;
  hisa_success_data_value: string;
  telco_success_airtime_count: number;
  telco_success_airtime_value: string;
  telco_success_data_count: number;
  telco_success_data_value: string;
}
