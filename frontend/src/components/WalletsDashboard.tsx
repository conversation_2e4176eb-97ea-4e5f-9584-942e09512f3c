import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Container, Form, Modal, Row, Table, Badge, Spinner } from 'react-bootstrap';
import axios from 'axios';

type SourceKey = 'hisa-one' | 'hisa-two';

interface WalletItem {
  id?: number | string;
  user_id?: number | string;
  amount?: number | string;
  user?: { id?: number | string; name?: string; email?: string };
  [k: string]: any;
}

interface ReconciliationData {
  opening_balance: number;
  closing_balance: number;
  total_debit: number;
  total_credit: number;
  expected_closing_balance: number;
  difference: number;
  balanced: boolean;
}

interface Props {
  baseURL: string;
  token: string;
}

const extractWallets = (apiResp: any): WalletItem[] => {
  if (!apiResp) return [];
  const data = apiResp.data; // backend wraps in { status_code, status, data, ... }
  if (Array.isArray(data)) return data;
  if (data && Array.isArray(data.data)) return data.data;
  return [];
};

const extractOverallBalance = (apiResp: any): number => {
  return typeof apiResp?.overall_balance === 'number' ? apiResp.overall_balance : 0;
};

const WalletsDashboard: React.FC<Props> = ({ baseURL, token }) => {
  const [loading, setLoading] = useState(false);
  const [source, setSource] = useState<SourceKey>('hisa-one');

  const [walletsOne, setWalletsOne] = useState<WalletItem[]>([]);
  const [walletsTwo, setWalletsTwo] = useState<WalletItem[]>([]);
  const [overallOne, setOverallOne] = useState<number>(0);
  const [overallTwo, setOverallTwo] = useState<number>(0);

  const [filterType, setFilterType] = useState<string>('today');
  const [fromDate, setFromDate] = useState<string>('');
  const [toDate, setToDate] = useState<string>('');

  const [reconLoading, setReconLoading] = useState<boolean>(false);
  const [reconResult, setReconResult] = useState<ReconciliationData | null>(null);
  const [reconUser, setReconUser] = useState<{ id?: string | number; name?: string; email?: string } | null>(null);
  const [showReconModal, setShowReconModal] = useState(false);

  const headers = useMemo(() => ({ Authorization: `Bearer ${token}` }), [token]);

  const fetchWallets = async () => {
    setLoading(true);
    try {
      const [one, two] = await Promise.all([
        axios.get(`${baseURL}/hisa-one/wallets`, { headers }),
        axios.get(`${baseURL}/hisa-two/wallets`, { headers }),
      ]);
      setWalletsOne(extractWallets(one.data));
      setWalletsTwo(extractWallets(two.data));
      setOverallOne(extractOverallBalance(one.data));
      setOverallTwo(extractOverallBalance(two.data));
    } catch (e) {
      console.error('Error fetching wallets:', e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWallets();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const items = source === 'hisa-one' ? walletsOne : walletsTwo;
  const overall = source === 'hisa-one' ? overallOne : overallTwo;

  const handleOpenRecon = (item: WalletItem) => {
    const u = item.user || { id: item.user_id, name: undefined, email: undefined };
    setReconUser({ id: u?.id ?? item.user_id, name: u?.name, email: u?.email });
    setFilterType('today');
    setFromDate('');
    setToDate('');
    setReconResult(null);
    setShowReconModal(true);
  };

  const handleReconcile = async () => {
    if (!reconUser?.id) return;
    setReconLoading(true);
    try {
      const params: Record<string, string> = { filter_type: filterType };
      if (filterType === 'custom') {
        if (!fromDate || !toDate) {
          setReconLoading(false);
          return;
        }
        params.from_date = fromDate;
        params.to_date = toDate;
      }
      const path = source === 'hisa-one' ? 'hisa-one' : 'hisa-two';
      const url = `${baseURL}/${path}/users/${reconUser.id}/wallet-reconciliation`;
      const { data } = await axios.get(url, { headers, params });
      const d = data?.data || {};
      setReconResult({
        opening_balance: d.opening_balance ?? 0,
        closing_balance: d.closing_balance ?? 0,
        total_debit: d.total_debit ?? 0,
        total_credit: d.total_credit ?? 0,
        expected_closing_balance: d.expected_closing_balance ?? 0,
        difference: d.difference ?? 0,
        balanced: !!d.balanced,
      });
    } catch (e) {
      console.error('Error reconciling wallet:', e);
    } finally {
      setReconLoading(false);
    }
  };

  return (
    <Container className="mt-4">
      <Row className="mb-3" xs={1} md={3}>
        <Col>
          <Card className="shadow-sm">
            <Card.Body>
              <Card.Title>Hisa One Overall</Card.Title>
              <Card.Text className="fs-4 fw-bold">NGN {overallOne.toLocaleString()}</Card.Text>
            </Card.Body>
          </Card>
        </Col>
        <Col>
          <Card className="shadow-sm">
            <Card.Body>
              <Card.Title>Hisa Two Overall</Card.Title>
              <Card.Text className="fs-4 fw-bold">NGN {overallTwo.toLocaleString()}</Card.Text>
            </Card.Body>
          </Card>
        </Col>
        <Col>
          <Card className="shadow-sm">
            <Card.Body>
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <Card.Title>{source === 'hisa-one' ? 'Hisa One' : 'Hisa Two'} Selected</Card.Title>
                  <Card.Text className="mb-0">Current shown overall: NGN {overall.toLocaleString()}</Card.Text>
                </div>
                <div className="d-flex gap-2">
                  <Form.Select size="sm" value={source} onChange={(e) => setSource(e.target.value as SourceKey)}>
                    <option value="hisa-one">Hisa One</option>
                    <option value="hisa-two">Hisa Two</option>
                  </Form.Select>
                  <Button variant="outline-primary" size="sm" onClick={fetchWallets} disabled={loading}>
                    {loading ? <Spinner size="sm" /> : 'Refresh'}
                  </Button>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Card className="shadow">
        <Card.Header className="d-flex justify-content-between align-items-center">
          <div className="fw-bold">Users and Current Balances</div>
          <div className="text-muted">{items.length} records</div>
        </Card.Header>
        <Card.Body className="p-0">
          <Table hover responsive className="mb-0">
            <thead>
              <tr>
                <th>User</th>
                <th>Email</th>
                <th>User ID</th>
                <th className="text-end">Current Balance</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {items.map((w, idx) => {
                const u = w.user || {};
                const uid = (u as any).id ?? w.user_id;
                const name = (u as any).name || '-';
                const email = (u as any).email || '-';
                const amount = typeof w.amount === 'number' ? w.amount : parseInt(String(w.amount || 0));
                return (
                  <tr key={`${uid}-${idx}`}>
                    <td>{name}</td>
                    <td>{email}</td>
                    <td><Badge bg="secondary">{uid}</Badge></td>
                    <td className="text-end">NGN {isNaN(amount) ? 0 : amount.toLocaleString()}</td>
                    <td>
                      <Button size="sm" variant="primary" onClick={() => handleOpenRecon(w)}>
                        Reconcile Wallet
                      </Button>
                    </td>
                  </tr>
                );
              })}
              {items.length === 0 && (
                <tr>
                  <td colSpan={5} className="text-center text-muted py-4">No data</td>
                </tr>
              )}
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Reconciliation Modal */}
      <Modal show={showReconModal} onHide={() => setShowReconModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Wallet Reconciliation</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-2">
            <div className="small text-muted">User</div>
            <div className="fw-semibold">{reconUser?.name || reconUser?.email || reconUser?.id}</div>
          </div>
          <Row className="g-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>Filter Type</Form.Label>
                <Form.Select value={filterType} onChange={(e) => setFilterType(e.target.value)}>
                  <option value="today">Today</option>
                  <option value="yesterday">Yesterday</option>
                  <option value="current_week">Current Week</option>
                  <option value="last_week">Last Week</option>
                  <option value="current_month">Current Month</option>
                  <option value="last_month">Last Month</option>
                  <option value="current_quarter">Current Quarter</option>
                  <option value="last_quarter">Last Quarter</option>
                  <option value="custom">Custom</option>
                </Form.Select>
              </Form.Group>
            </Col>
            {filterType === 'custom' && (
              <>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label>From Date</Form.Label>
                    <Form.Control type="date" value={fromDate} onChange={(e) => setFromDate(e.target.value)} />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label>To Date</Form.Label>
                    <Form.Control type="date" value={toDate} onChange={(e) => setToDate(e.target.value)} />
                  </Form.Group>
                </Col>
              </>
            )}
          </Row>

          {reconResult && (
            <Card className="mt-3">
              <Card.Body>
                <Row>
                  <Col md={6}>Opening: <strong>NGN {reconResult.opening_balance.toLocaleString()}</strong></Col>
                  <Col md={6}>Closing: <strong>NGN {reconResult.closing_balance.toLocaleString()}</strong></Col>
                  <Col md={6} className="mt-2">Debit: <strong className="text-danger">NGN {reconResult.total_debit.toLocaleString()}</strong></Col>
                  <Col md={6} className="mt-2">Credit: <strong className="text-success">NGN {reconResult.total_credit.toLocaleString()}</strong></Col>
                  <Col md={12} className="mt-2">Expected Closing: <strong>NGN {reconResult.expected_closing_balance.toLocaleString()}</strong></Col>
                  <Col md={12} className="mt-2">Spotted Difference: <strong className={reconResult.difference === 0 ? 'text-success' : 'text-danger'}>NGN {reconResult.difference.toLocaleString()}</strong></Col>
                  <Col md={12} className="mt-2">Balanced: {reconResult.balanced ? <Badge bg="success">Yes</Badge> : <Badge bg="danger">No</Badge>}</Col>
                </Row>
              </Card.Body>
            </Card>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowReconModal(false)}>Close</Button>
          <Button variant="primary" onClick={handleReconcile} disabled={reconLoading}>
            {reconLoading ? <Spinner size="sm" /> : 'Reconcile'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default WalletsDashboard;

