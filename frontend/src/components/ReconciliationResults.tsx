import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import { ReconciliationResult } from '../types';

// Use the same baseURL as defined in App.tsx
const baseURL = import.meta.env.DEV
  ? 'http://localhost:8080'
  : import.meta.env.VITE_API_BASE_URL;

interface ReconciliationResultsProps {
  result: ReconciliationResult;
  onBack: () => void;
}

const ReconciliationResults: React.FC<ReconciliationResultsProps> = ({ result, onBack }) => {
  // Function to ensure URLs are absolute
  const getFullUrl = (url: string) => {
    if (url.startsWith('http')) return url;
    return `${baseURL}${url.startsWith('/') ? '' : '/'}${url}`;
  };

  return (
    <Container className="py-5">
      <Card className="shadow-lg border-0 rounded-4 results-card mx-auto">
        <Card.Header className="bg-primary bg-gradient text-white p-4 rounded-top-4 border-0 d-flex justify-content-between align-items-center">
          <div>
            <h4 className="mb-0 fw-bold">HISA Reconciliation Results</h4>
            <p className="mb-0 mt-2 opacity-75 fw-light">Transaction reconciliation summary and detailed analysis</p>
          </div>
          <Button variant="light" onClick={onBack} className="back-button">
            <i className="bi bi-arrow-left me-2"></i>
            Back to Form
          </Button>
        </Card.Header>

        <Card.Body className="p-5">
          {/* Summary Statistics */}
          <div className="stats-section mb-5">
            <h5 className="section-title mb-4">Transaction Overview</h5>
            <Row>
              <Col md={6}>
                <div className="stat-card">
                  <div className="stat-header">
                    <i className="bi bi-list-check stat-icon"></i>
                    <h6>All Transactions (Success & Failed)</h6>
                  </div>
                  <div className="stat-content">
                    <div className="stat-item">
                      <span className="stat-label">HISA Transactions</span>
                      <span className="stat-value">{result.hisa_all_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Transactions</span>
                      <span className="stat-value">{result.telco_all_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">HISA Total Value</span>
                      <span className="stat-value">{result.hisa_all_value}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Total Value</span>
                      <span className="stat-value">{result.telco_all_value}</span>
                    </div>
                  </div>
                </div>
              </Col>
              <Col md={6}>
                <div className="stat-card">
                  <div className="stat-header">
                    <i className="bi bi-check-circle stat-icon"></i>
                    <h6>Successful Transactions Only</h6>
                  </div>
                  <div className="stat-content">
                    <div className="stat-item">
                      <span className="stat-label">HISA Successful Transactions</span>
                      <span className="stat-value">{result.hisa_success_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Successful Transactions</span>
                      <span className="stat-value">{result.telco_success_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">HISA Success Total</span>
                      <span className="stat-value">{result.hisa_total}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Success Total</span>
                      <span className="stat-value">{result.telco_total}</span>
                    </div>
                    <div className="stat-item highlight">
                      <span className="stat-label">Difference</span>
                      <span className={`stat-value ${result.difference.toString().startsWith('-') ? 'text-danger' : 'text-success'}`}>
                        {result.difference}
                      </span>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </div>

          {/* Matching Statistics */}
          <div className="stats-section mb-5">
            <h5 className="section-title mb-4">Matching Analysis</h5>
            <div className="stat-card">
              <div className="stat-header">
                <i className="bi bi-diagram-3 stat-icon"></i>
                <h6>Transaction Matching Details</h6>
              </div>
              <div className="stat-content">
                <Row>
                  <Col md={6}>
                    <div className="stat-item">
                      <span className="stat-label">Total Unique in HISA</span>
                      <span className="stat-value">{result.matching_statistics.total_unique_in_HISA}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Total Unique in Telco</span>
                      <span className="stat-value">{result.matching_statistics.total_unique_in_TELCO}</span>
                    </div>
                  </Col>
                  <Col md={6}>
                    <div className="stat-item">
                      <span className="stat-label">Matching Transactions</span>
                      <span className="stat-value text-success">{result.matching_statistics.matching_transactions}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Transactions in HISA but not in Telco</span>
                      <span className="stat-value text-warning">
                        {result.matching_statistics.match_combination_keys_in_HISA_but_not_in_TELCO}
                      </span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Transactions in Telco but not in HISA</span>
                      <span className="stat-value text-warning">
                        {result.matching_statistics.match_combination_keys_in_TELCO_but_not_in_HISA}
                      </span>
                    </div>
                  </Col>
                </Row>
              </div>
            </div>
          </div>

          {/* Discrepancy Summary */}
          <div className="stats-section mb-5">
            <h5 className="section-title mb-4">Discrepancy Analysis</h5>
            <Row>
              <Col md={6}>
                <div className="stat-card">
                  <div className="stat-header">
                    <i className="bi bi-exclamation-triangle stat-icon text-warning"></i>
                    <h6>Missing Records</h6>
                  </div>
                  <div className="stat-content">
                    <div className="stat-item">
                      <span className="stat-label">Missing in HISA</span>
                      <span className="stat-value text-danger">{result.missing_in_hisa_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Extra in HISA</span>
                      <span className="stat-value text-danger">{result.extra_in_hisa_count}</span>
                    </div>
                  </div>
                </div>
              </Col>
              <Col md={6}>
                <div className="stat-card">
                  <div className="stat-header">
                    <i className="bi bi-files stat-icon text-warning"></i>
                    <h6>Duplicate Records</h6>
                  </div>
                  <div className="stat-content">
                    <div className="stat-item">
                      <span className="stat-label">HISA Duplicates</span>
                      <span className="stat-value text-warning">{result.local_duplicates_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Duplicates</span>
                      <span className="stat-value text-warning">{result.telco_duplicates_count}</span>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </div>

          {/* Transaction Type Analysis */}
          <div className="stats-section mb-5">
            <h5 className="section-title mb-4">Transaction Type Analysis</h5>
            <Row>
              <Col md={6}>
                <div className="stat-card">
                  <div className="stat-header">
                    <i className="bi bi-phone stat-icon"></i>
                    <h6>Airtime Transactions</h6>
                  </div>
                  <div className="stat-content">
                    <div className="stat-item">
                      <span className="stat-label">HISA Airtime Transactions</span>
                      <span className="stat-value">{result.hisa_airtime_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Airtime Transactions</span>
                      <span className="stat-value">{result.telco_airtime_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">HISA Airtime Value</span>
                      <span className="stat-value">{result.hisa_airtime_value}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Airtime Value</span>
                      <span className="stat-value">{result.telco_airtime_value}</span>
                    </div>
                  </div>
                </div>
              </Col>
              <Col md={6}>
                <div className="stat-card">
                  <div className="stat-header">
                    <i className="bi bi-wifi stat-icon"></i>
                    <h6>Data Transactions</h6>
                  </div>
                  <div className="stat-content">
                    <div className="stat-item">
                      <span className="stat-label">HISA Data Transactions</span>
                      <span className="stat-value">{result.hisa_data_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Data Transactions</span>
                      <span className="stat-value">{result.telco_data_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">HISA Data Value</span>
                      <span className="stat-value">{result.hisa_data_value}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Data Value</span>
                      <span className="stat-value">{result.telco_data_value}</span>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </div>

          {/* Successful Transactions by Type */}
          <div className="stats-section mb-5">
            <h5 className="section-title mb-4">Successful Transactions by Type</h5>
            <Row>
              <Col md={6}>
                <div className="stat-card">
                  <div className="stat-header">
                    <i className="bi bi-check-circle-fill stat-icon text-success"></i>
                    <h6>Successful Airtime Transactions</h6>
                  </div>
                  <div className="stat-content">
                    <div className="stat-item">
                      <span className="stat-label">HISA Successful Airtime Count</span>
                      <span className="stat-value">{result.hisa_success_airtime_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Successful Airtime Count</span>
                      <span className="stat-value">{result.telco_success_airtime_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">HISA Successful Airtime Value</span>
                      <span className="stat-value">{result.hisa_success_airtime_value}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Successful Airtime Value</span>
                      <span className="stat-value">{result.telco_success_airtime_value}</span>
                    </div>
                  </div>
                </div>
              </Col>
              <Col md={6}>
                <div className="stat-card">
                  <div className="stat-header">
                    <i className="bi bi-check-circle-fill stat-icon text-success"></i>
                    <h6>Successful Data Transactions</h6>
                  </div>
                  <div className="stat-content">
                    <div className="stat-item">
                      <span className="stat-label">HISA Successful Data Count</span>
                      <span className="stat-value">{result.hisa_success_data_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Successful Data Count</span>
                      <span className="stat-value">{result.telco_success_data_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">HISA Successful Data Value</span>
                      <span className="stat-value">{result.hisa_success_data_value}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Successful Data Value</span>
                      <span className="stat-value">{result.telco_success_data_value}</span>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </div>

          {/* Failed Transactions */}
          <div className="stats-section mb-5">
            <h5 className="section-title mb-4">Failed Transactions</h5>
            <div className="stat-card">
              <div className="stat-header">
                <i className="bi bi-x-circle-fill stat-icon text-danger"></i>
                <h6>Failed Transactions Summary</h6>
              </div>
              <div className="stat-content">
                <Row>
                  <Col md={6}>
                    <div className="stat-item">
                      <span className="stat-label">HISA Failed Transactions</span>
                      <span className="stat-value">{result.hisa_failed_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">HISA Failed Value</span>
                      <span className="stat-value">{result.hisa_failed_value}</span>
                    </div>
                  </Col>
                  <Col md={6}>
                    <div className="stat-item">
                      <span className="stat-label">Telco Failed Transactions</span>
                      <span className="stat-value">{result.telco_failed_count}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Telco Failed Value</span>
                      <span className="stat-value">{result.telco_failed_value}</span>
                    </div>
                  </Col>
                </Row>
              </div>
            </div>
          </div>

          {/* Download Reports */}
          <div className="stats-section">
            <h5 className="section-title mb-4">Generated Reports</h5>
            <div className="download-card">
              <div className="text-center mb-4">
                <p className="fs-5 text-muted">Download detailed reports for further analysis</p>
              </div>
              <div className="download-buttons">
                <Button
                  variant="outline-primary"
                  href={getFullUrl(result.duplicates_file_url)}
                  target="_blank"
                  disabled={!result.duplicates_file_url}
                  className="download-button"
                >
                  <i className="bi bi-download me-2"></i>
                  Download Duplicates Report
                </Button>
                <Button
                  variant="outline-primary"
                  href={getFullUrl(result.missing_file_url)}
                  target="_blank"
                  disabled={!result.missing_file_url}
                  className="download-button"
                >
                  <i className="bi bi-download me-2"></i>
                  Download Missing Records Report
                </Button>
                <Button
                  variant="outline-success"
                  href={getFullUrl(result.user_consumption_file_url)}
                  target="_blank"
                  disabled={!result.user_consumption_file_url}
                  className="download-button"
                >
                  <i className="bi bi-person-lines-fill me-2"></i>
                  Download User Consumption Report
                </Button>
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default ReconciliationResults;
