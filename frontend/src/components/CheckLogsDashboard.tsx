import React, { useEffect, use<PERSON>emo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Container, Form, Modal, Row, <PERSON>, <PERSON>ge, Spin<PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import axios from 'axios';

type SourceKey = 'hisa_one' | 'hisa_two';

interface UserItem {
  id?: number | string;
  name?: string;
  email?: string;
  wallet?: { id?: number | string; user_id?: number | string; amount?: number | string };
  [k: string]: any;
}

interface CheckLogsResult {
  user_id: string;
  target_date: string;
  hisa_source: string;
  download_result: string;
  wallet_balances: {
    opening_balance: number;
    closing_balance: number;
    expected_closing_balance: number;
    difference: number;
    balance_matches: boolean;
  };
  transaction_analysis: {
    total_transactions: number;
    total_value_naira: number;
    by_status: Record<string, { count: number; value_naira: number }>;
    by_type: Record<string, { count: number; value_naira: number }>;
  };
  reconciliation_summary: {
    total_transactions: number;
    successful_transactions: number;
    successful_value_naira: number;
    balance_reconciled: boolean;
  };
}

interface Props {
  baseURL: string;
  token: string;
}

const extractUsers = (apiResp: any): UserItem[] => {
  if (!apiResp) return [];
  const data = apiResp.data;
  if (Array.isArray(data)) return data;
  if (data && Array.isArray(data.data)) return data.data;
  return [];
};

const CheckLogsDashboard: React.FC<Props> = ({ baseURL, token }) => {
  const [loading, setLoading] = useState(false);
  const [source, setSource] = useState<SourceKey>('hisa_one');

  const [usersOne, setUsersOne] = useState<UserItem[]>([]);
  const [usersTwo, setUsersTwo] = useState<UserItem[]>([]);

  const [checkLoading, setCheckLoading] = useState<boolean>(false);
  const [checkResult, setCheckResult] = useState<CheckLogsResult | null>(null);
  const [checkUser, setCheckUser] = useState<{ id?: string | number; name?: string; email?: string } | null>(null);
  const [showCheckModal, setShowCheckModal] = useState(false);
  const [targetDate, setTargetDate] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [availableDates, setAvailableDates] = useState<string[]>([]);

  const headers = useMemo(() => ({ Authorization: `Bearer ${token}` }), [token]);

  // Generate available dates (last 30 days, excluding today)
  const generateAvailableDates = useMemo(() => {
    const dates: string[] = [];
    const today = new Date();

    for (let i = 1; i <= 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
    }

    return dates;
  }, []);

  useEffect(() => {
    setAvailableDates(generateAvailableDates);
  }, [generateAvailableDates]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const [one, two] = await Promise.all([
        axios.get(`${baseURL}/hisa-one/users`, { headers }),
        axios.get(`${baseURL}/hisa-two/users`, { headers }),
      ]);
      setUsersOne(extractUsers(one.data));
      setUsersTwo(extractUsers(two.data));
    } catch (e) {
      console.error('Error fetching users:', e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const items = source === 'hisa_one' ? usersOne : usersTwo;

  const handleOpenCheck = (item: UserItem) => {
    setCheckUser({ id: item.id, name: item.name, email: item.email });
    setTargetDate('');
    setCheckResult(null);
    setError('');
    setShowCheckModal(true);
  };

  const handleCheckLogs = async () => {
    if (!checkUser?.id || !targetDate) {
      setError('Please select a date');
      return;
    }

    // Additional validation (though the dropdown should prevent invalid dates)
    const today = new Date().toISOString().split('T')[0];
    if (targetDate >= today) {
      setError('Invalid date selected. Please select a past date.');
      return;
    }

    setCheckLoading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('user_id', String(checkUser.id));
      formData.append('target_date', targetDate);
      formData.append('hisa_source', source);

      const { data } = await axios.post(`${baseURL}/check-logs/`, formData, {
        headers: {
          ...headers,
          'Content-Type': 'multipart/form-data',
        },
      });

      if (data.status) {
        setCheckResult(data.data);
      } else {
        setError(data.error || 'Failed to check logs');
      }
    } catch (e: any) {
      console.error('Error checking logs:', e);
      if (e.response?.data?.error) {
        setError(e.response.data.error);
      } else {
        setError('Failed to check logs. Please try again.');
      }
    } finally {
      setCheckLoading(false);
    }
  };

  return (
    <Container className="mt-4">
      <Row className="mb-3">
        <Col>
          <Card className="shadow-sm">
            <Card.Body>
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <Card.Title>Check User Logs</Card.Title>
                  <Card.Text className="mb-0">
                    Select a user to download and analyze their transaction logs
                  </Card.Text>
                </div>
                <div className="d-flex gap-2">
                  <Form.Select size="sm" value={source} onChange={(e) => setSource(e.target.value as SourceKey)}>
                    <option value="hisa_one">Hisa One</option>
                    <option value="hisa_two">Hisa Two</option>
                  </Form.Select>
                  <Button variant="outline-primary" size="sm" onClick={fetchUsers} disabled={loading}>
                    {loading ? <Spinner size="sm" /> : 'Refresh'}
                  </Button>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Card className="shadow">
        <Card.Header className="d-flex justify-content-between align-items-center">
          <div className="fw-bold">Users - {source === 'hisa_one' ? 'Hisa One' : 'Hisa Two'}</div>
          <div className="text-muted">{items.length} records</div>
        </Card.Header>
        <Card.Body className="p-0">
          <Table hover responsive className="mb-0">
            <thead>
              <tr>
                <th>User</th>
                <th>Email</th>
                <th>User ID</th>
                <th>Current Balance</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {items.map((user, idx) => {
                const uid = user.id;
                const name = user.name || '-';
                const email = user.email || '-';
                const walletAmount = user.wallet?.amount || 0;
                const amount = typeof walletAmount === 'number' ? walletAmount : parseInt(String(walletAmount));
                return (
                  <tr key={`${uid}-${idx}`}>
                    <td>{name}</td>
                    <td>{email}</td>
                    <td><Badge bg="secondary">{uid}</Badge></td>
                    <td className="text-end">₦{isNaN(amount) ? 0 : amount.toLocaleString()}</td>
                    <td>
                      <Button size="sm" variant="warning" onClick={() => handleOpenCheck(user)}>
                        Check Logs
                      </Button>
                    </td>
                  </tr>
                );
              })}
              {items.length === 0 && (
                <tr>
                  <td colSpan={5} className="text-center text-muted py-4">No data</td>
                </tr>
              )}
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Check Logs Modal */}
      <Modal show={showCheckModal} onHide={() => setShowCheckModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Check User Logs</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-3">
            <div className="small text-muted">User</div>
            <div className="fw-semibold">{checkUser?.name || checkUser?.email || checkUser?.id}</div>
            <div className="small text-muted">Source: {source === 'hisa_one' ? 'Hisa One' : 'Hisa Two'}</div>
          </div>

          <Row className="g-3 mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>Target Date</Form.Label>
                <Form.Select
                  value={targetDate}
                  onChange={(e) => setTargetDate(e.target.value)}
                >
                  <option value="">Select a date...</option>
                  {availableDates.map((date) => {
                    const dateObj = new Date(date);
                    const formattedDate = dateObj.toLocaleDateString('en-US', {
                      weekday: 'short',
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    });
                    return (
                      <option key={date} value={date}>
                        {formattedDate} ({date})
                      </option>
                    );
                  })}
                </Form.Select>
                <Form.Text className="text-muted">
                  Select from the last 30 days (current date not included)
                </Form.Text>
              </Form.Group>
            </Col>
          </Row>

          {error && (
            <Alert variant="danger" className="mb-3">
              {error}
            </Alert>
          )}

          {checkResult && (
            <div className="mt-3">
              <h6>Download Result</h6>
              <Alert variant="info" className="mb-3">
                {checkResult.download_result}
              </Alert>

              <Row>
                <Col md={6}>
                  <Card className="mb-3">
                    <Card.Header>
                      <h6 className="mb-0">Wallet Balances</h6>
                    </Card.Header>
                    <Card.Body>
                      <div className="mb-2">
                        <strong>Opening Balance:</strong> ₦{checkResult.wallet_balances.opening_balance.toLocaleString()}
                      </div>
                      <div className="mb-2">
                        <strong>Closing Balance:</strong> ₦{checkResult.wallet_balances.closing_balance.toLocaleString()}
                      </div>
                      <div className="mb-2">
                        <strong>Expected Closing:</strong> ₦{checkResult.wallet_balances.expected_closing_balance.toLocaleString()}
                      </div>
                      <div className="mb-2">
                        <strong>Difference:</strong>
                        <span className={checkResult.wallet_balances.difference === 0 ? 'text-success' : 'text-danger'}>
                          {' '}₦{checkResult.wallet_balances.difference.toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <strong>Balance Matches:</strong>{' '}
                        {checkResult.wallet_balances.balance_matches ? (
                          <Badge bg="success">Yes</Badge>
                        ) : (
                          <Badge bg="danger">No</Badge>
                        )}
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={6}>
                  <Card className="mb-3">
                    <Card.Header>
                      <h6 className="mb-0">Transaction Summary</h6>
                    </Card.Header>
                    <Card.Body>
                      <div className="mb-2">
                        <strong>Total Transactions:</strong> {checkResult.reconciliation_summary.total_transactions.toLocaleString()}
                      </div>
                      <div className="mb-2">
                        <strong>Successful:</strong> {checkResult.reconciliation_summary.successful_transactions.toLocaleString()}
                      </div>
                      <div className="mb-2">
                        <strong>Successful Value:</strong> ₦{checkResult.reconciliation_summary.successful_value_naira.toLocaleString()}
                      </div>
                      <div>
                        <strong>Reconciled:</strong>{' '}
                        {checkResult.reconciliation_summary.balance_reconciled ? (
                          <Badge bg="success">Yes</Badge>
                        ) : (
                          <Badge bg="danger">No</Badge>
                        )}
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              {checkResult.transaction_analysis.by_status && (
                <Card>
                  <Card.Header>
                    <h6 className="mb-0">Transaction Breakdown by Status</h6>
                  </Card.Header>
                  <Card.Body>
                    <Row>
                      {Object.entries(checkResult.transaction_analysis.by_status).map(([status, data]) => (
                        <Col md={6} key={status} className="mb-2">
                          <div>
                            <strong>{status}:</strong> {data.count.toLocaleString()} transactions, ₦{data.value_naira.toLocaleString()}
                          </div>
                        </Col>
                      ))}
                    </Row>
                  </Card.Body>
                </Card>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCheckModal(false)}>Close</Button>
          <Button variant="warning" onClick={handleCheckLogs} disabled={checkLoading || !targetDate}>
            {checkLoading ? <Spinner size="sm" /> : 'Check Logs'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default CheckLogsDashboard;
