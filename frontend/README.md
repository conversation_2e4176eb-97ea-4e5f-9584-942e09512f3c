# Hisa Reconciliation Manager Frontend

This is the frontend application for the Hisa Reconciliation Manager. It provides a user interface for uploading and reconciling files between Hisa and various telco providers (MTN, Airtel, and GLO).

## Setup

1. Install dependencies:

```bash
npm install
```

2. Start the development server:

```bash
npm run dev
```

The application will be available at `URL` (or another port if 5173 is in use).

## Usage

1. Select the telco provider (MTN, Airtel, or GLO)
2. Choose the target date for reconciliation
3. Upload the Hisa Admin file
4. Upload the corresponding telco file
5. Optionally check "Use Transaction ID" if you want to use transaction IDs for matching
6. Click "Reconcile" to process the files
7. View the results in the table below
8. Download the generated reports using the provided links

## Development

The application is built using:

- React with TypeScript
- Vite as the build tool
- React Bootstrap for UI components
- Axios for API calls

## API Integration

The frontend communicates with the backend API running at `baseURL`. Make sure the backend server is running before using the application.
