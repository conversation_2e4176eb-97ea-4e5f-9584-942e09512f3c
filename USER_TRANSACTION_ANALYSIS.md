# User Transaction Analysis Function

## Overview

The `get_user_transaction_summary()` function in `app/utils.py` provides detailed transaction analytics for individual users based on CSV transaction data. It analyzes transaction counts and values by status and type.

## Function Signature

```python
def get_user_transaction_summary(
    user_id: str,
    csv_file_path: str = "reports/hisa_logs/2025-09-09/2025-09-09_transactions.csv"
) -> dict
```

## Parameters

- **user_id** (str): The user ID to analyze transactions for
- **csv_file_path** (str, optional): Path to the CSV file containing transaction data. Defaults to the 2025-09-09 transactions file.

## Return Value

Returns a dictionary with the following structure:

### Successful Response
```python
{
    "user_id": "5",
    "total_transactions": 14273,
    "total_value_naira": 19675657.00,
    "by_status": {
        "Successful": {"count": 14100, "value_naira": 19316902.00},
        "Failed": {"count": 91, "value_naira": 243855.00},
        "InvalidMsisdn": {"count": 79, "value_naira": 112700.00},
        "Pending": {"count": 3, "value_naira": 2200.00}
    },
    "by_type": {
        "Airtime": {"count": 14273, "value_naira": 19675657.00}
    }
}
```

### No Transactions Found
```python
{
    "user_id": "99999",
    "total_transactions": 0,
    "total_value_naira": 0.0,
    "by_status": {},
    "by_type": {},
    "message": "No transactions found for user_id: 99999"
}
```

### Error Response
```python
{
    "user_id": "5",
    "error": "Error message describing what went wrong"
}
```

## Usage Examples

### Basic Usage
```python
from app.utils import get_user_transaction_summary

# Analyze transactions for user ID 5
result = get_user_transaction_summary("5")

if "error" not in result:
    print(f"User {result['user_id']} has {result['total_transactions']:,} transactions")
    print(f"Total value: ₦{result['total_value_naira']:,.2f}")
else:
    print(f"Error: {result['error']}")
```

### Analyzing Successful Transactions
```python
result = get_user_transaction_summary("5")

if "Successful" in result['by_status']:
    successful = result['by_status']['Successful']
    success_rate = (successful['count'] / result['total_transactions']) * 100
    print(f"Success rate: {success_rate:.1f}%")
    print(f"Successful transactions: {successful['count']:,} worth ₦{successful['value_naira']:,.2f}")
```

### Using Custom CSV File
```python
# Analyze from a different CSV file
result = get_user_transaction_summary("5", "path/to/other/transactions.csv")
```

### Comparing Multiple Users
```python
users = ["5", "20", "13"]
for user_id in users:
    result = get_user_transaction_summary(user_id)
    if result['total_transactions'] > 0:
        successful_count = result['by_status'].get('Successful', {}).get('count', 0)
        success_rate = (successful_count / result['total_transactions']) * 100
        print(f"User {user_id}: {result['total_transactions']:,} transactions, {success_rate:.1f}% success rate")
```

## Transaction Statuses

The function recognizes the following transaction statuses:
- **Successful**: Completed transactions
- **Failed**: Failed transactions
- **InvalidMsisdn**: Transactions with invalid phone numbers
- **Pending**: Transactions still being processed

## Transaction Types

Currently supports:
- **Airtime**: Airtime top-up transactions

## Data Format

The function expects CSV files with the following columns:
- Column 3: `amount` (in kobo, automatically converted to naira)
- Column 6: `type` (transaction type, e.g., "Airtime")
- Column 8: `status` (transaction status)
- Column 11: `user_id` (user identifier)

## Example Output

When you run the test script (`python test_user_transaction_summary.py`), you'll see output like:

```
📊 Most Active User (ID: 5)
==========================
👤 User ID: 5
📊 Total Transactions: 14,273
💰 Total Value: ₦19,675,657.00
✅ Success Rate: 98.8% (14,100 successful)

📈 Breakdown by Status:
  • Successful: 14,100 transactions (98.8%), ₦19,316,902.00
  • Failed: 91 transactions (0.6%), ₦243,855.00
  • InvalidMsisdn: 79 transactions (0.6%), ₦112,700.00
  • Pending: 3 transactions (0.0%), ₦2,200.00

🏷️  Breakdown by Type:
  • Airtime: 14,273 transactions (100.0%), ₦19,675,657.00
```

## Error Handling

The function handles various error scenarios:
- File not found
- Invalid CSV format
- Missing columns
- Data type conversion errors

All errors are returned in the response dictionary with an "error" key rather than raising exceptions.
