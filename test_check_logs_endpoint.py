#!/usr/bin/env python3
"""
Test script for the check-logs endpoint.
"""

import requests
import json
from datetime import date, timedelta

# Configuration
BASE_URL = "http://localhost:8080"
USERNAME = "admin"  # Replace with actual username
PASSWORD = "password"  # Replace with actual password


def login():
    """Login and get access token."""
    login_data = {"username": USERNAME, "password": PASSWORD}

    response = requests.post(f"{BASE_URL}/login/", data=login_data)
    if response.status_code == 200:
        return response.json()["data"]["access_token"]
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None


def test_users_endpoints():
    """Test the users endpoints to see available users."""
    token = login()
    if not token:
        print("Failed to login")
        return

    headers = {"Authorization": f"Bearer {token}"}

    print("Testing users endpoints...")

    try:
        # Test hisa-one users
        response = requests.get(f"{BASE_URL}/hisa-one/users", headers=headers)
        print(f"Hisa One users response status: {response.status_code}")
        if response.status_code == 200:
            users_data = response.json()
            users = users_data.get("data", {}).get("data", [])
            print(f"Found {len(users)} Hisa One users")
            if users:
                print(f"First user example: {users[0]}")

        # Test hisa-two users
        response = requests.get(f"{BASE_URL}/hisa-two/users", headers=headers)
        print(f"Hisa Two users response status: {response.status_code}")
        if response.status_code == 200:
            users_data = response.json()
            users = users_data.get("data", {}).get("data", [])
            print(f"Found {len(users)} Hisa Two users")
            if users:
                print(f"First user example: {users[0]}")

    except Exception as e:
        print(f"❌ Exception occurred: {e}")


def test_check_logs_endpoint():
    """Test the check-logs endpoint."""
    token = login()
    if not token:
        print("Failed to login")
        return

    headers = {"Authorization": f"Bearer {token}"}

    # First get a user ID from the users endpoint
    try:
        response = requests.get(f"{BASE_URL}/hisa-one/users", headers=headers)
        if response.status_code == 200:
            users_data = response.json()
            users = users_data.get("data", {}).get("data", [])
            if users:
                user_id = str(users[0].get("id", "1"))
                print(f"Using user ID: {user_id} ({users[0].get('name', 'Unknown')})")
            else:
                user_id = "1"
                print("No users found, using default user ID: 1")
        else:
            user_id = "1"
            print("Failed to fetch users, using default user ID: 1")
    except:
        user_id = "1"
        print("Error fetching users, using default user ID: 1")

    # Test with multiple dates to check local cache functionality
    test_dates = [
        (date.today() - timedelta(days=1)).strftime("%Y-%m-%d"),  # Yesterday
        (date.today() - timedelta(days=2)).strftime("%Y-%m-%d"),  # 2 days ago
        "2025-09-09",  # Known date with existing logs
    ]

    for test_date in test_dates:
        print(f"\n{'='*60}")
        print(f"Testing with date: {test_date}")
        print("=" * 60)

        test_data = {
            "user_id": user_id,
            "target_date": test_date,
            "hisa_source": "hisa_one",
        }

        print(f"Testing check-logs endpoint with data: {test_data}")

        try:
            response = requests.post(
                f"{BASE_URL}/check-logs/", data=test_data, headers=headers
            )
            print(f"Response status: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print("✅ Success!")

                # Show download result to see if it used local cache
                download_result = result.get("data", {}).get("download_result", "")
                print(f"📁 Download result: {download_result}")

                # Show key metrics
                data = result.get("data", {})
                wallet_balances = data.get("wallet_balances", {})
                reconciliation = data.get("reconciliation_summary", {})

                print(
                    f"💰 Opening Balance: ₦{wallet_balances.get('opening_balance', 0):,.2f}"
                )
                print(
                    f"💰 Closing Balance: ₦{wallet_balances.get('closing_balance', 0):,.2f}"
                )
                print(
                    f"📊 Total Transactions: {reconciliation.get('total_transactions', 0):,}"
                )
                print(
                    f"✅ Successful Transactions: {reconciliation.get('successful_transactions', 0):,}"
                )
                print(
                    f"🎯 Balance Reconciled: {'Yes' if reconciliation.get('balance_reconciled') else 'No'}"
                )

                # Show wallet transaction details if available
                if "wallet_transaction_analysis" in data:
                    wallet_analysis = data["wallet_transaction_analysis"]
                    print(
                        f"💳 Wallet Credits: ₦{wallet_analysis.get('total_credits', 0):,.2f} ({wallet_analysis.get('credit_count', 0):,} transactions)"
                    )
                    print(
                        f"💳 Wallet Debits: ₦{wallet_analysis.get('total_debits', 0):,.2f} ({wallet_analysis.get('debit_count', 0):,} transactions)"
                    )
                    print(
                        f"💳 Wallet Net Change: ₦{wallet_analysis.get('net_change', 0):,.2f}"
                    )
                    print(
                        f"💳 Total Wallet Transactions: {wallet_analysis.get('total_transactions', 0):,}"
                    )

                # Show enhanced reconciliation summary
                if "total_wallet_credits" in reconciliation:
                    print(
                        f"🔄 Total Wallet Credits: ₦{reconciliation.get('total_wallet_credits', 0):,.2f}"
                    )
                    print(
                        f"🔄 Total Wallet Debits: ₦{reconciliation.get('total_wallet_debits', 0):,.2f}"
                    )
                    print(
                        f"🔄 Wallet Net Change: ₦{reconciliation.get('wallet_net_change', 0):,.2f}"
                    )

            else:
                print("❌ Error:")
                print(response.text)

        except Exception as e:
            print(f"❌ Exception occurred: {e}")

        # Small delay between requests
        import time

        time.sleep(1)


def test_validation():
    """Test endpoint validation."""
    token = login()
    if not token:
        print("Failed to login")
        return

    headers = {"Authorization": f"Bearer {token}"}

    print("\n" + "=" * 50)
    print("Testing validation...")

    # Test with current date (should fail)
    today = date.today().strftime("%Y-%m-%d")
    test_data = {"user_id": "5", "target_date": today, "hisa_source": "hisa_one"}

    print(f"Testing with current date: {today}")
    response = requests.post(f"{BASE_URL}/check-logs/", data=test_data, headers=headers)
    print(f"Response status: {response.status_code}")
    if response.status_code == 400:
        print("✅ Correctly rejected current date")
        print(response.json().get("error"))
    else:
        print("❌ Should have rejected current date")

    # Test with invalid hisa_source
    yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
    test_data = {
        "user_id": "5",
        "target_date": yesterday,
        "hisa_source": "invalid_source",
    }

    print(f"\nTesting with invalid hisa_source: invalid_source")
    response = requests.post(f"{BASE_URL}/check-logs/", data=test_data, headers=headers)
    print(f"Response status: {response.status_code}")
    if response.status_code == 400:
        print("✅ Correctly rejected invalid hisa_source")
        print(response.json().get("error"))
    else:
        print("❌ Should have rejected invalid hisa_source")


if __name__ == "__main__":
    print("🧪 Testing Check Logs Endpoint")
    print("=" * 50)

    test_users_endpoints()
    print("\n" + "=" * 50)
    test_check_logs_endpoint()
    test_validation()

    print("\n" + "=" * 50)
    print("Test completed!")
