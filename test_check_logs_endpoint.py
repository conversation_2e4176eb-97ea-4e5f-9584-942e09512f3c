#!/usr/bin/env python3
"""
Test script for the check-logs endpoint.
"""

import requests
import json
from datetime import date, timedelta

# Configuration
BASE_URL = "http://localhost:8080"
USERNAME = "admin"  # Replace with actual username
PASSWORD = "password"  # Replace with actual password

def login():
    """Login and get access token."""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/login/", data=login_data)
    if response.status_code == 200:
        return response.json()["data"]["access_token"]
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def test_check_logs_endpoint():
    """Test the check-logs endpoint."""
    token = login()
    if not token:
        print("Failed to login")
        return
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # Test data
    yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
    test_data = {
        "user_id": "5",  # Using user ID 5 which we know has transactions
        "target_date": yesterday,
        "hisa_source": "hisa_one"
    }
    
    print(f"Testing check-logs endpoint with data: {test_data}")
    
    try:
        response = requests.post(f"{BASE_URL}/check-logs/", data=test_data, headers=headers)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(json.dumps(result, indent=2))
        else:
            print("❌ Error:")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")

def test_validation():
    """Test endpoint validation."""
    token = login()
    if not token:
        print("Failed to login")
        return
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    print("\n" + "="*50)
    print("Testing validation...")
    
    # Test with current date (should fail)
    today = date.today().strftime("%Y-%m-%d")
    test_data = {
        "user_id": "5",
        "target_date": today,
        "hisa_source": "hisa_one"
    }
    
    print(f"Testing with current date: {today}")
    response = requests.post(f"{BASE_URL}/check-logs/", data=test_data, headers=headers)
    print(f"Response status: {response.status_code}")
    if response.status_code == 400:
        print("✅ Correctly rejected current date")
        print(response.json().get("error"))
    else:
        print("❌ Should have rejected current date")
    
    # Test with invalid hisa_source
    yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
    test_data = {
        "user_id": "5",
        "target_date": yesterday,
        "hisa_source": "invalid_source"
    }
    
    print(f"\nTesting with invalid hisa_source: invalid_source")
    response = requests.post(f"{BASE_URL}/check-logs/", data=test_data, headers=headers)
    print(f"Response status: {response.status_code}")
    if response.status_code == 400:
        print("✅ Correctly rejected invalid hisa_source")
        print(response.json().get("error"))
    else:
        print("❌ Should have rejected invalid hisa_source")

if __name__ == "__main__":
    print("🧪 Testing Check Logs Endpoint")
    print("=" * 50)
    
    test_check_logs_endpoint()
    test_validation()
    
    print("\n" + "="*50)
    print("Test completed!")
